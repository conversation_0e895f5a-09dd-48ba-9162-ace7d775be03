{"name": "@askinfosec/database-drizzle", "version": "0.0.1", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "files": ["dist"], "scripts": {"clean": "rm -rf dist tsconfig.tsbuildinfo", "build": "pnpm run clean && tsc", "dev": "tsc --watch", "test": "jest --config ./jest.config.cjs", "test:watch": "jest --config ./jest.config.cjs --watch", "test:int": "cross-env RUN_DB_TESTS=true jest --config ./jest.config.cjs -- src/tests/integration/email-auth-code.repository.int.test.ts", "test:int:file": "cross-env RUN_DB_TESTS=true jest --config ./jest.config.cjs -- src/tests/integration/file.repository.int.test.ts"}, "dependencies": {"@askinfosec/shared": "workspace:*", "@askinfosec/types": "workspace:*", "drizzle-orm": "^0.44.5", "pg": "^8.16.3"}, "devDependencies": {"@askinfosec/jest-config": "workspace:*", "@askinfosec/typescript-config": "workspace:*", "@types/jest": "^30.0.0", "@types/pg": "^8.15.5", "cross-env": "10.0.0", "dotenv": "^17.2.2", "drizzle-kit": "^0.31.4", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsx": "^4.20.5", "typescript": "^5.0.0"}}