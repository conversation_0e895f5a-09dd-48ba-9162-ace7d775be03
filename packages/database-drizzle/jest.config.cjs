/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest/presets/default-esm',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  modulePaths: ['<rootDir>'],
  moduleDirectories: ['node_modules', '<rootDir>', '<rootDir>/src'],
  extensionsToTreatAsEsm: ['.ts'],
  resolver: 'ts-jest-resolver',
  transform: {
    '^.+\\.tsx?$': ['ts-jest', { useESM: true, tsconfig: '<rootDir>/tsconfig.jest.json' }],
  },
  moduleNameMapper: {
    // Map ESM-style .js imports in TS sources to their TS paths
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  testMatch: [
    '**/*.test.ts',
    '**/*.spec.ts',
    '**/*.int.test.ts',
  ],
};


