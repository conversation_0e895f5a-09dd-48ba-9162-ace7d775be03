# Database Drizzle Package

A scalable, maintainable database abstraction layer built on Drizzle ORM with repository pattern and PostgreSQL Row Level Security (RLS) support.

## Overview

This package provides:
- **Connection Management**: Singleton pool management with RLS-aware context switching
- **Repository Pattern**: Domain-oriented data access with clean separation of concerns
- **RLS Support**: Automatic tenant isolation and bypass capabilities for admin operations
- **Type Safety**: Full TypeScript support with schema-first approach
- **NestJS Integration**: Request-scoped providers and dependency injection

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    NestJS Application                       │
├─────────────────────────────────────────────────────────────┤
│  Controllers → Services → Repositories → ConnectionManager  │
├─────────────────────────────────────────────────────────────┤
│                    Database Drizzle                        │
│  ┌─────────────────┐  ┌──────────────┐  ┌───────────────┐  │
│  │  Repositories   │  │   Schemas    │  │ Connection    │  │
│  │                 │  │              │  │ Manager       │  │
│  │ - Organization  │  │ - User       │  │               │  │
│  │ - User          │  │ - Org        │  │ - RLS Context │  │
│  │ - Files         │  │ - Member     │  │ - Pool Mgmt   │  │
│  └─────────────────┘  └──────────────┘  └───────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     PostgreSQL + RLS                       │
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### Connection Manager

Manages database connections with RLS-aware context switching:

```typescript
import { createConnectionManager } from '@askinfosec/database-drizzle';

const connectionManager = createConnectionManager(
  process.env.DATABASE_URL!,
  true // Enable RLS
);

// Tenant-scoped connection
const db = await connectionManager.getDbWithTenant('org_123');

// Admin bypass connection  
const adminDb = await connectionManager.getDbBypassRls();
```

### Repository Pattern

Domain-oriented data access with clean interfaces:

```typescript
import { OrganizationRepository } from '@askinfosec/database-drizzle';

class MyService {
  constructor(private orgRepo: OrganizationRepository) {}
  
  async getOrganization(id: string) {
    return await this.orgRepo.findById(id);
  }
  
  async createOrganization(data: OrganizationCreateInput) {
    return await this.orgRepo.create(data);
  }
}
```

### Schema Definitions

Type-safe schema definitions with automatic inference:

```typescript
import { users, organizations, type User, type NewUser } from '@askinfosec/database-drizzle';

// Inferred types available for all schemas
const user: User = await db.select().from(users).where(eq(users.id, 'user_123'));
```

## NestJS Integration

### Module Setup

The `DatabaseDrizzleModule` is a global module that provides:

```typescript
import { DatabaseDrizzleModule } from '@askinfosec/database-drizzle';

@Module({
  imports: [DatabaseDrizzleModule],
  // ... other config
})
export class AppModule {}
```

### Request-Scoped Context

The module automatically configures RLS context based on request data:

```typescript
@Controller('organizations')
@UseGuards(RlsContextGuard)
export class OrganizationController {
  constructor(private orgRepo: OrganizationRepository) {}
  
  @Get(':id')
  async getOrganization(@Param('id') id: string) {
    // Automatically uses tenant-scoped connection
    return await this.orgRepo.findById(id);
  }
  
  @RlsBypass()
  @Get('admin/all')
  async getAllOrganizations() {
    // Uses RLS bypass connection
    return await this.orgRepo.findMany();
  }
}
```

### Context Sources

The system extracts tenant context from multiple sources (in priority order):

1. **JWT Payload**: `request.user.organizationId`
2. **Headers**: `x-organization-id`
3. **Query Parameters**: `?organizationId=org_123`

### RLS Bypass

Use the `@RlsBypass()` decorator for admin operations:

```typescript
@RlsBypass()
@Get('admin/stats')
async getGlobalStats() {
  // This bypasses RLS and can access all data
  return await this.orgRepo.count();
}
```

## Repository Development

### Creating New Repositories

1. **Extend BaseRepository**:

```typescript
import { BaseRepository, ITenantRepository } from '@askinfosec/database-drizzle';

export class UserRepository 
  extends BaseRepository 
  implements ITenantRepository<User, CreateUserInput, UpdateUserInput> 
{
  async findById(id: string): Promise<User | null> {
    return this.executeQuery(async () => {
      const result = await this.db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);
      
      return result[0] || null;
    }, 'findById');
  }
  
  // Implement other required methods...
}
```

2. **Register in Module**:

```typescript
{
  provide: UserRepository,
  scope: Scope.REQUEST,
  useFactory: (dbContext: DbContext) => new UserRepository(dbContext.db),
  inject: [DRIZZLE_DB_CONTEXT],
}
```

### Repository Best Practices

- **Domain Methods**: Expose business-oriented methods, not generic CRUD
- **Error Handling**: Use `executeQuery()` wrapper for consistent error handling
- **Transactions**: Use `transaction()` method for multi-operation consistency
- **Type Safety**: Always use inferred types from schema definitions

## Environment Configuration

### Required Variables

```bash
# Database connection
DATABASE_URL=postgresql://user:password@localhost:5432/database

# RLS Configuration
ENABLE_RLS=true  # Enable in production
NODE_ENV=production  # Automatically enables RLS
```

### Development Setup

```bash
# Local development (RLS disabled by default)
DATABASE_URL=postgresql://localhost:5432/askinfosec_dev
ENABLE_RLS=false

# Testing with RLS
DATABASE_URL=postgresql://localhost:5432/askinfosec_test
ENABLE_RLS=true
```

## Testing

### Unit Testing Repositories

```typescript
import { createConnectionManager } from '@askinfosec/database-drizzle';

describe('OrganizationRepository', () => {
  let connectionManager: ConnectionManager;
  let repository: OrganizationRepository;
  
  beforeAll(async () => {
    connectionManager = createConnectionManager(
      process.env.TEST_DATABASE_URL!,
      true // Enable RLS for testing
    );
    
    const db = await connectionManager.getDbWithTenant('test_org');
    repository = new OrganizationRepository(db);
  });
  
  it('should find organization by ID', async () => {
    const org = await repository.findById('org_123');
    expect(org).toBeDefined();
  });
});
```

### Integration Testing

Run repository integration tests against a real PostgreSQL database. Tests are skipped unless the required environment is present.

Required env vars (place in the monorepo root `.env`):

```bash
# RLS-enabled test connection (user must have access to the target schema)
DATABASE_URL_RLS_USER=postgresql://user:password@localhost:5432/askinfosec_test

# Test tenant/user context
TEST_ORG_ID=test_org
TEST_USER_ID=test_user

# Optional: bypass RLS (default is false)
TEST_BYPASS_RLS=false
```

Execute from `packages/database-drizzle/`:

```bash
# Email auth code repository integration test
RUN_DB_TESTS=true pnpm test -- src/tests/integration/email-auth-code.repository.int.test.ts

# File repository (bytea) integration test
RUN_DB_TESTS=true pnpm test -- src/tests/integration/file.repository.int.test.ts

# Run all integration tests in the folder
RUN_DB_TESTS=true pnpm test -- src/tests/integration/*.int.test.ts
```

What these tests do:
- Email auth code: creates, fetches, marks as used, and verifies persistence via direct DB reads.
- File repository (bytea): uploads base64 content, verifies stored `bytea` as `Buffer` exactly matches original bytes, and cleans up the test record.

```typescript
import { Test } from '@nestjs/testing';
import { DatabaseDrizzleModule } from './database-drizzle.module';

describe('DatabaseDrizzle Integration', () => {
  let app: INestApplication;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [DatabaseDrizzleModule],
    }).compile();
    
    app = module.createNestApplication();
    await app.init();
  });
  
  it('should provide organization repository', () => {
    const repo = app.get(OrganizationRepository);
    expect(repo).toBeInstanceOf(OrganizationRepository);
  });
});
```

## Monitoring & Metrics

### Connection Pool Metrics

```typescript
const metrics = connectionManager.getConnectionMetrics();
console.log('Pool Stats:', metrics.pool);
console.log('RLS State:', metrics.rls);
```

### Health Checks

```typescript
const isHealthy = await connectionManager.testConnection();
if (!isHealthy) {
  // Handle connection issues
}
```

## Migration Strategy

This package is designed to work alongside existing Prisma setup:

1. **Keep Prisma for**: Migrations, schema management, data seeding
2. **Use Drizzle for**: Runtime queries, repositories, performance-critical operations
3. **Gradual Migration**: Port modules one at a time from Prisma to Drizzle repositories

## Performance Considerations

- **Connection Pooling**: Automatic pool management with configurable limits
- **Query Optimization**: Direct SQL generation without ORM overhead
- **RLS Efficiency**: Minimal overhead for tenant context switching
- **Type Safety**: Zero runtime cost for TypeScript inference

## Troubleshooting

### Common Issues

1. **RLS Permission Denied**: Ensure `app.current_organization_id` is set correctly
2. **Connection Leaks**: Always use repository methods, avoid direct DB access
3. **Type Errors**: Ensure schema definitions match database structure
4. **Performance**: Monitor connection pool metrics for optimization

### Debug Mode

```typescript
// Enable query logging
const connectionManager = createConnectionManager(connectionString, hasRLS);
await connectionManager.getPool().query('SET log_statement = "all"');
```

## Contributing

When adding new repositories or schemas:

1. Define schema in `src/schemas/schema.ts`
2. Create repository in `src/repositories/`
3. Add to module providers
4. Export from `src/index.ts`
5. Update this README with examples


# SAMPLE INTEGRATION TEST
```bash
RUN_DB_TESTS=true pnpm test -- src/tests/integration/email-auth-code.repository.int.test.ts
```