/*
 Integration test for FileRepository using a real database.
 - Requires env vars: DATABASE_URL_RLS_USER, TEST_ORG_ID, TEST_USER_ID
 - Set RUN_DB_TESTS=true to enable running this test.
 - Cleans up inserted data.
*/

import path from 'path';
import { config as dotenvConfig } from 'dotenv';
// Load repo root .env explicitly
const envPath = path.resolve(__dirname, '../../../../../.env');
dotenvConfig({ path: envPath });

// Mock ESM nanoid usage from @askinfosec/shared to avoid Jest ESM issues
jest.mock('@askinfosec/shared', () => ({
  generateId: () => 'file-int-test-id',
}));

import { createConnectionManager } from '../../lib/connection-manager';
import type { SessionContext } from '../../lib/base-repository';
import { FileRepository } from '../../repositories/file.repository';
import { files } from '../../schemas/schema';
import { eq } from 'drizzle-orm';

// Print loaded env (mask connection string)
(() => {
  const conn = process.env.DATABASE_URL_RLS_USER || '';
  const masked = conn ? conn.substring(0, 20) + '...' : undefined;
  console.log('[INT][env][file]', {
    RUN_DB_TESTS: process.env.RUN_DB_TESTS,
    HAS_CONN: !!process.env.DATABASE_URL_RLS_USER,
    DATABASE_URL_RLS_USER_PREFIX: masked,
    TEST_ORG_ID: process.env.TEST_ORG_ID,
    TEST_USER_ID: process.env.TEST_USER_ID,
    TEST_BYPASS_RLS: process.env.TEST_BYPASS_RLS,
  });
})();

const RUN_DB_TESTS = process.env.RUN_DB_TESTS === 'true';
const HAS_CONN = Boolean(process.env.DATABASE_URL_RLS_USER);

const maybeDescribe = RUN_DB_TESTS && HAS_CONN ? describe : describe.skip;

maybeDescribe('Integration: FileRepository (bytea upload/download)', () => {
  const connectionString = process.env.DATABASE_URL_RLS_USER as string;
  const TEST_ORG_ID = process.env.TEST_ORG_ID || 'test_org';
  const TEST_USER_ID = process.env.TEST_USER_ID || 'test_user';

  let repo: FileRepository;
  let cleanupId: string | null = null;
  let closeFn: null | (() => Promise<void>) = null;
  let dbRef: any;

  const context: SessionContext = {
    organizationId: TEST_ORG_ID,
    userId: TEST_USER_ID,
    bypassRls: process.env.TEST_BYPASS_RLS === 'true',
  };

  beforeAll(async () => {
    const manager = createConnectionManager(connectionString, true);
    const db = await manager.getDb();
    dbRef = db;
    closeFn = async () => manager.close();
    repo = new FileRepository(db);
  });

  afterAll(async () => {
    try {
      if (cleanupId) {
        // Best-effort cleanup via repository API
        try {
          await repo.deleteFiles([cleanupId], context);
        } catch (e) {
          // Fallback to direct delete if repo cleanup fails
          await dbRef.delete(files).where(eq(files.id, cleanupId));
        }
      }
    } finally {
      if (closeFn) await closeFn();
    }
  });

  it('should upload base64 content and download exact buffer (bytea)', async () => {
    const originalContent = 'Hello bytea integration!';
    const originalBuffer = Buffer.from(originalContent, 'utf8');
    const base64 = originalBuffer.toString('base64');

    // Create file with bytea buffer via base64 input
    const created = await repo.createFileWithBuffer(
      { name: 'int-file.txt', bufferBase64: base64 },
      context,
    );
    expect(created).toBeTruthy();
    cleanupId = created.id;

    // Fetch the file and validate stored buffer
    const fetched = await repo.findById(created.id, context);
    expect(fetched).toBeTruthy();
    expect(fetched!.bufferFile).toBeTruthy();
    expect(Buffer.isBuffer(fetched!.bufferFile)).toBe(true);

    const fetchedBuffer = fetched!.bufferFile as Buffer;
    expect(fetchedBuffer.equals(originalBuffer)).toBe(true);
  }, 20000);
});


