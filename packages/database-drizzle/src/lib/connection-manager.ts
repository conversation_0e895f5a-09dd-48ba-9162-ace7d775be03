import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { IDatabaseConfig, IDatabaseConnection } from '@askinfosec/types';
import { schema } from '../schemas/schema.js';

export type DrizzleDB = NodePgDatabase<typeof schema>;

export interface ConnectionMetrics {
  pool: {
    totalCount: number;
    idleCount: number;
    waitingCount: number;
  };
}

export class ConnectionManager implements IDatabaseConnection {
  private pool: Pool;
  private db: DrizzleDB;
  private config: IDatabaseConfig;

  constructor(config: IDatabaseConfig) {
    this.config = config;
    this.pool = new Pool({
      connectionString: config.connectionString,
      max: Number(process.env.PG_POOL_MAX || 20),
      idleTimeoutMillis: Number(process.env.PG_IDLE_TIMEOUT_MS || 30000),
      connectionTimeoutMillis: Number(process.env.PG_CONN_TIMEOUT_MS || 10000),
      keepAlive: true,
    });

    this.db = drizzle(this.pool, {
      schema,
    });
    
    // Attach connection manager reference for debugging
    (this.db as any).__connectionManager__ = this;
  }

  /**
   * Get the base database connection
   * RLS configuration is handled by the repository layer
   */
  async getDb(): Promise<DrizzleDB> {
    return this.db;
  }

  /**
   * Get connection pool metrics for monitoring
   */
  getConnectionMetrics(): ConnectionMetrics {
    return {
      pool: {
        totalCount: this.pool.totalCount,
        idleCount: this.pool.idleCount,
        waitingCount: this.pool.waitingCount,
      },
    };
  }

  /**
   * Get database configuration
   */
  getDatabaseConfig(): IDatabaseConfig {
    return { ...this.config };
  }

  /**
   * Close all database connections
   */
  async close(): Promise<void> {
    await this.pool.end();
  }

  /**
   * Get raw pool for advanced operations (use sparingly)
   */
  getPool(): Pool {
    return this.pool;
  }

  /**
   * Test database connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.pool.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }
}

/**
 * Factory function to create ConnectionManager instance
 */
export function createConnectionManager(connectionString?: string, hasRLS: boolean = true): ConnectionManager {
  // Use RLS user by default, fallback to root user, then DATABASE_URL_RLS_USER
  const connString = connectionString || process.env.DATABASE_URL_RLS_USER;
    
  if (!connString) {
    throw new Error('Database connection string is required.');
  }
  
  console.log(`🔗 [CONNECTION] Creating connection manager:`, {
    hasRLS,
    connectionString: connString.substring(0, 20) + '...', // Log partial string for security
    envVars: {
      hasRLSUser: !!process.env.DATABASE_URL_RLS_USER
    }
  });
  
  const config: IDatabaseConfig = {
    connectionString: connString,
    hasRLS,
    schema: 'public',
  };
  
  return new ConnectionManager(config);
}
