import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';

export class UploadBase64Dto {
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name!: string;

  // Base64 string (no data: prefix expected). We allow characters A-Z, a-z, 0-9, +, /, =
  @IsString()
  @MinLength(10)
  @Matches(/^[A-Za-z0-9+/=]+$/)
  bufferBase64!: string;

  @IsOptional()
  @IsString()
  // hex-encoded sha256 preferred
  @Matches(/^[a-fA-F0-9]{64}$/, { message: 'checksum must be sha256 hex' })
  checksum?: string;

  @IsOptional()
  @IsString()
  documentType?: string;

  @IsOptional()
  @IsString()
  // Optional extracted text/HTML content provided by client for text-based uploads
  content?: string;
}
