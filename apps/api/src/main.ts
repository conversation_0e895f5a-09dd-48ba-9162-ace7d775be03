import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

import { Logger } from './modules/logging/services/logging.service';

// We'll use configuration directly here without DI since we are outside the module context
import getConfig from './config';
const config = getConfig();

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { bufferLogs: true });
  const logger = app.get(Logger);
  app.useLogger(logger);

  // Configure body size limits
  app.use(require('express').json({ limit: '10mb' }));
  app.use(require('express').urlencoded({ limit: '10mb', extended: true }));

  // Configure raw body parsing for webhook endpoints BEFORE other middleware
  app.use('/v1/webhooks/stripe', (req, res, next) => {
    const chunks: Buffer[] = [];
    req.on('data', (chunk: Buffer) => {
      chunks.push(chunk);
    });
    req.on('end', () => {
      (req as any).rawBody = Buffer.concat(chunks);
      logger.log(`Raw body captured at app level: ${(req as any).rawBody.length} bytes`);
      next();
    });
  });

  app.enableCors({
    origin: config.security.corsOrigins,
    credentials: true,
  });

  // URI versioning results in routes like /v1/... (no global 'api' prefix)
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  if (config.swagger.enable) {
    const swaggerConfig = new DocumentBuilder()
      .setTitle('AskInfosec API')
      .setDescription('The AskInfosec API documentation')
      .setVersion('1.0')
      .addTag('api')
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('docs', app, document);
  }

  logger.log('API bootstrap complete');
  await app.listen(config.port);
}
bootstrap();
