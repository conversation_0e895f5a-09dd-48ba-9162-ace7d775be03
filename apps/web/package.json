{"name": "@web", "private": true, "scripts": {"dev": "next dev --turbo --port 3000", "dev:https": "next dev --turbo --experimental-https --experimental-https-key ../../certificates/localhost-key.pem --experimental-https-cert ../../certificates/localhost.pem --port 3000", "build": "pnpm --filter=@askinfosec/database build && pnpm copy:tinymce && next build", "prisma:generate": "pnpm --filter=@askinfosec/database build && prisma generate --schema=../../packages/database/prisma/schema.prisma", "start": "next start", "copy:tinymce": "copyfiles -u 1 './node_modules/tinymce/**/*' './public/'", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "check-format": "prettier --check .", "check-lint": "next lint", "check-types": "tsc --noEmit", "check-all": "pnpm check-format && pnpm check-lint && pnpm check-types && pnpm build"}, "dependencies": {"@askinfosec/database": "workspace:*", "@askinfosec/types": "workspace:*", "@auth/prisma-adapter": "2.9.1", "@ducanh2912/next-pwa": "10.2.9", "@edge-csrf/nextjs": "2.5.3-cloudflare-rc1", "@formatjs/intl-localematcher": "0.6.1", "@hello-pangea/dnd": "18.0.1", "@hookform/resolvers": "5.1.1", "@huggingface/inference": "4.0.4", "@langchain/cohere": "0.3.4", "@langchain/community": "0.3.46", "@langchain/core": "0.3.58", "@langchain/openai": "0.5.13", "@logtail/next": "0.2.0", "@logtail/pino": "0.5.5", "@mux/mux-node": "11.1.0", "@mux/mux-player-react": "3.4.0", "@panva/hkdf": "1.2.1", "@paralleldrive/cuid2": "2.2.2", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-hover-card": "1.1.14", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@react-email/components": "0.0.42", "@react-email/render": "1.1.2", "@react-email/tailwind": "1.0.5", "@reduxjs/toolkit": "2.8.2", "@simplewebauthn/browser": "^13.1.0", "@simplewebauthn/server": "^13.1.1", "@simplewebauthn/typescript-types": "^8.3.4", "@stripe/react-stripe-js": "3.7.0", "@stripe/stripe-js": "7.3.1", "@tanstack/react-table": "8.21.3", "@tinymce/tinymce-react": "6.2.1", "@uploadthing/react": "^7.3.1", "@vercel/analytics": "^1.5.0", "axios": "^1.9.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "csv-parser": "3.2.0", "d3-dsv": "^3.0.1", "date-fns": "^3.6.0", "deep-object-diff": "1.1.9", "docx-preview": "0.3.5", "dotenv": "^16.5.0", "exceljs": "4.4.0", "file-saver": "2.0.5", "file-type": "21.0.0", "fs-extra": "^11.3.0", "i18next": "25.2.1", "i18next-resources-to-backend": "1.2.1", "jose": "6.0.11", "jsdom": "^26.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "3.10.1", "langchain": "0.3.28", "lodash": "4.17.21", "lucide-react": "0.514.0", "mammoth": "1.9.1", "nanoid": "5.1.5", "negotiator": "1.0.0", "next": "15.5.2", "next-auth": "beta", "next-pwa": "5.6.0", "next-safe-action": "8.0.2", "next-themes": "0.4.6", "nodemailer": "7.0.3", "openai": "5.12.0", "path-to-regexp": "8.2.0", "pdf-parse": "1.1.1", "pdfjs-dist": "^5.3.31", "pino": "9.7.0", "pino-abstract-transport": "2.0.0", "pino-pretty": "13.0.0", "quill": "2.0.3", "react": "19.1.0", "react-circular-progressbar": "2.2.0", "react-day-picker": "9.7.0", "react-dom": "19.1.0", "react-dropzone": "14.3.8", "react-email": "4.0.16", "react-hook-form": "7.57.0", "react-i18next": "15.5.3", "react-intersection-observer": "9.16.0", "react-markdown": "10.1.0", "react-mentions": "4.4.10", "react-quilljs": "^2.0.5", "react-syntax-highlighter": "15.6.1", "react-tag-autocomplete": "7.5.0", "react-textarea-autosize": "8.5.9", "react-tooltip": "5.29.0", "readable-web-to-node-stream": "5.0.0", "recharts": "2.15.3", "rehype-minify-whitespace": "6.0.2", "rehype-raw": "7.0.0", "rehype-sanitize": "6.0.0", "remark-breaks": "4.0.0", "remark-gfm": "4.0.1", "remark-math": "6.0.0", "remark-rehype": "11.1.2", "remark-toc": "9.0.0", "server-only": "0.0.1", "sonner": "^2.0.5", "stripe": "18.5.0", "swr": "2.3.3", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "tinymce": "7.9.1", "uploadthing": "7.7.2", "vaul": "1.1.2", "zod": "3.25.63", "zod-form-data": "3.0.0"}, "devDependencies": {"@askinfosec/eslint-config": "workspace:*", "@askinfosec/typescript-config": "workspace:*", "@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@eslint/compat": "^1.3.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@jest/globals": "30.0.0", "@release-it/conventional-changelog": "10.0.1", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@types/chai-as-promised": "8.0.2", "@types/d3-dsv": "^3.0.7", "@types/eslint__js": "^9.14.0", "@types/file-saver": "2.0.7", "@types/jest": "30.0.0", "@types/jsdom": "^21.1.7", "@types/lodash": "4.17.17", "@types/negotiator": "0.6.4", "@types/node": "22.16.3", "@types/nodemailer": "6.4.17", "@types/pg": "8.15.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.0", "@types/react-mentions": "4.4.1", "@types/react-syntax-highlighter": "15.5.13", "@types/stripe": "^8.0.417", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "auto-changelog": "2.5.0", "chai-as-promised": "8.0.1", "copyfiles": "2.4.1", "dotenv-cli": "8.0.0", "encoding": "0.1.13", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "10.1.5", "eslint-plugin-check-file": "3.3.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-testing-library": "^7.5.2", "jest": "30.0.0", "jest-mock-extended": "4.0.0-beta1", "postcss": "8.5.5", "prisma": "6.15.0", "prisma-kysely": "1.8.0", "release-it": "19.0.3", "rimraf": "^6.0.1", "sass": "1.89.2", "tailwindcss": "4.1.10", "ts-jest": "^29.4.0", "ts-jest-resolver": "2.0.1", "ts-node": "10.9.2", "tw-animate-css": "^1.3.6", "type-fest": "4.41.0", "typescript": "5.8.3", "typescript-eslint": "8.34.0", "xlsx": "^0.18.5"}}