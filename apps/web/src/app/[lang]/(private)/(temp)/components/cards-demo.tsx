/* Card examples extracted from ShadCN-UI card component. */

"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  <PERSON><PERSON>,
} from "@/components";

export default function CardsDemo() {
  return (
    <section id="cards" className="space-y-4">
      <h2 className="text-2xl font-semibold">Cards</h2>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Basic Card</CardTitle>
            <CardDescription>Simple title & description.</CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              Cards provide a flexible & extensible content container with multiple variants &
              options.
            </p>
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button size="sm">Action</Button>
          </CardFooter>
        </Card>

        <Card className="border-dashed border-primary">
          <CardHeader>
            <CardTitle>Dashed Border</CardTitle>
            <CardDescription>Demonstrates custom className usage.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              The <code>className</code> prop can be used to tweak appearance while retaining base
              styles.
            </p>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
