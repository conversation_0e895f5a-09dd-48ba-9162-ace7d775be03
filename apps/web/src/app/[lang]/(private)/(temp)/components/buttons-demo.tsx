/* Button examples extracted from ShadCN-UI button component.
   NOTE: This file is only for docs / playground usage. */

"use client";

import { Button } from "@/components";

const buttonVariants = [
  { label: "Primary", variant: "default" },
  { label: "Secondary", variant: "secondary" },
  { label: "Destructive", variant: "destructive" },
  { label: "Outline", variant: "outline" },
  { label: "Ghost", variant: "ghost" },
  { label: "Link", variant: "link" },
] as const;

export default function ButtonsDemo() {
  return (
    <section id="buttons" className="space-y-4">
      <h2 className="text-2xl font-semibold">Buttons</h2>
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6">
        {buttonVariants.map(({ label, variant }) => (
          <div key={variant} className="flex flex-col items-center gap-2">
            <Button variant={variant as any} className="w-full">
              {label}
            </Button>
            <code className="text-xs text-muted-foreground mt-1">variant="{variant}"</code>
          </div>
        ))}
      </div>
    </section>
  );
}
