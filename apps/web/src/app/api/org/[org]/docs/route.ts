import { type NextRequest, NextResponse } from "next/server";
import { saveFileContent } from "@/services/server/file";
import {
  withLoggingRouteHandler,
  withRbacRouteHandler,
  with<PERSON>essionRouteHandler,
  NextRouteHandler,
} from "@/app/api/middlewares";
import { getDocs, deleteDoc } from "@/services/server/docs";
import { guardedPrisma, tenantGuardedPrisma } from "@/lib/prisma";
import { ParsedPath, parse } from "path";
import { ACCEPTED_DOCUMENT_EXTENSIONS } from "@/lib/utils";
import JSzip from "jszip";
import { type Session } from "next-auth";

/**
 *
 * @param org - organization
 * @returns deleted file
 */
const deleteDocsHandler: NextRouteHandler = async (
  req: NextRequest & { session: Session },
  { params }: { params: { org: string } },
) => {
  const { ids } = await req.json();
  const userId = req.session.user.id;
  const db = guardedPrisma({
    orgId: params.org as string,
    userId,
    checkMembership: false,
  });

  if (ids.length <= 0) throw Error("No selected files");

  const deletedFiles = await Promise.all(
    ids.map(async ({ _id, org }: any) => {
      const del = await deleteDoc({ org, file: _id, db });

      return del;
    }),
  );
  return NextResponse.json(deletedFiles);
};
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(
      withRbacRouteHandler({ allowedRoles: ["owner", "admin"] })(deleteDocsHandler),
    ),
  );
  return await handler(request, { params: resolvedParams });
}

const getAllDocsHandler: NextRouteHandler = async (
  req: NextRequest & { session: Session },
  { params }: { params: { org: string } },
) => {
  const userId = req.session.user.id;
  const db = guardedPrisma({
    orgId: params.org as string,
    userId,
    checkMembership: false,
  });

  const docs = await getDocs(params.org as string, db);

  return NextResponse.json(docs);
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(withSessionRouteHandler()(getAllDocsHandler));
  return await handler(request, { params: resolvedParams });
}

/**
 * TODO: Document what this handler does
 */
const uploadHandler: NextRouteHandler = async (
  req: NextRequest & { session: Session },
  { params }: { params: { org: string } },
) => {
  const guardedPrisma = tenantGuardedPrisma(params.org);

  const userId = req.session.user.id;
  const formData = await req.formData();
  const file: File | null = formData.get("file") as unknown as File;
  if (!file) return NextResponse.json({ message: "invalid file" }, { status: 400 });
  const filePath: ParsedPath = parse(file.name);

  if (filePath.ext && !ACCEPTED_DOCUMENT_EXTENSIONS.includes(filePath.ext)) {
    return NextResponse.json(
      {
        message: `Please specify a content type. Currently supported values are: ${ACCEPTED_DOCUMENT_EXTENSIONS.join(
          ", ",
        )}.`,
      },
      { status: 400 },
    );
  }

  if (filePath.ext === ".zip") {
    const buffer = await file.arrayBuffer().then((ab) => Buffer.from(ab));
    const zip = await JSzip.loadAsync(buffer);

    const result = await Promise.all([
      ...Object.keys(zip.files).map(async (k) => {
        const filePath = parse(k);

        if (!filePath.name.startsWith(".")) {
          return saveFileContent(
            await zip.files[k].async("blob"),
            guardedPrisma,
            params.org,
            userId,
            filePath,
          );
        }
      }),
    ]);
    return NextResponse.json(result);
  } else {
    try {
      const result = await saveFileContent(file, guardedPrisma, params.org, userId, filePath);
      return NextResponse.json(result);
    } catch (error) {
      return NextResponse.json({ message: error }, { status: 400 });
    }
  }
};
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(withSessionRouteHandler()(uploadHandler));
  return await handler(request, { params: resolvedParams });
}
