"use client";

import React from "react";
import { DashboardSidebarProps } from "@/types/dashboard";
import { useOrgRoutes } from "@/hooks/use-org-routes";
import { useRBAC } from "@/providers/rbac-provider";
import { useOrganization } from "@/providers/organization-provider";
import { useSidebar } from "@/providers/sidebar-context";
import { buildOrgRoute } from "@/lib/routes";
import { SidebarLayout, SidebarNav } from "@/components/layout/sidebar";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { SidebarNavItem } from "@/types/sidebar";

export const DashboardSidebar: React.FC<DashboardSidebarProps> = ({
  isCollapsed,
  onClose,
  isMobile = false,
  currentPath = "",
}) => {
  const { userRole } = useRBAC();
  const {
    orgRoutes,
    orgSettingsRoute,
    hasOrganization,
    isRouteActive,
    isChildRouteActive,
    isLoading: modulesLoading,
  } = useOrgRoutes();
  const { isLoading: organizationLoading, currentOrganizationId } = useOrganization();
  const { setActiveSidebar } = useSidebar();

  if (organizationLoading || modulesLoading || !hasOrganization) {
    return (
      <div className="flex flex-col h-full bg-card no-border shadow-sm">
        <div className="flex items-center justify-between h-12 flex-shrink-0 bg-background">
          <div className="flex items-center space-x-3 px-4">
            <Skeleton className="w-9 h-9 rounded-lg" />
            <Skeleton className="h-6 w-24" />
          </div>
        </div>
        <nav className="flex-1 px-3 py-6 space-y-2 overflow-y-auto">
          <div className="mb-4">
            <Skeleton className="h-4 w-16" />
          </div>
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-3 px-2 py-2">
              <Skeleton className="w-5 h-5 rounded" />
              <Skeleton className="h-4 flex-1" />
            </div>
          ))}
        </nav>
      </div>
    );
  }

  const footer =
    (userRole === "owner" || userRole === "admin") && orgSettingsRoute ? (
      <button
        onClick={() => {
          setActiveSidebar("organization");
          if (currentOrganizationId) {
            window.location.href = buildOrgRoute(currentOrganizationId, "/settings/general");
          }
        }}
        className={cn(
          "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer h-10",
          isCollapsed ? "justify-center px-1 py-2" : "px-3 py-2",
          isRouteActive(orgSettingsRoute.path)
            ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
        )}
      >
        <orgSettingsRoute.icon className="h-6 w-6" />
        {!isCollapsed && <span className="ml-3">{orgSettingsRoute.name}</span>}
      </button>
    ) : null;

  return (
    <SidebarLayout
      layoutType="dashboard"
      isCollapsed={isCollapsed}
      isMobile={isMobile}
      onClose={onClose}
      footer={footer}
    >
      <SidebarNav
        items={orgRoutes as SidebarNavItem[]}
        isCollapsed={isCollapsed}
        currentPath={currentPath}
        isRouteActive={(p?: string) => (p ? isRouteActive(p) : false)}
        isChildRouteActive={(p?: string) => (p ? isChildRouteActive(p) : false)}
      />
    </SidebarLayout>
  );
};
