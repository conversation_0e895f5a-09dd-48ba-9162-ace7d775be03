"use client";

import React from "react";
import { Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { useAIPane } from "@/providers/ai-pane-provider";

export interface AskAIButtonProps {
  /**
   * Additional CSS classes to apply to the button
   */
  className?: string;
  /**
   * Whether the button is disabled
   */
  disabled?: boolean;
  /**
   * Click handler for the button (will be implemented in future phases)
   */
  onClick?: () => void;
  /**
   * Button size variant
   */
  size?: "sm" | "default" | "lg";
  /**
   * Button style variant
   */
  variant?: "default" | "outline" | "ghost";
  /**
   * Custom tooltip text (defaults to "Toggles the AI Pane")
   */
  tooltipText?: string;
  /**
   * Whether to show the sparkle icon
   */
  showIcon?: boolean;
}

export function AskAIButton({
  className,
  disabled = false,
  onClick,
  size = "default",
  variant = "outline",
  tooltipText = "Toggles the AI Pane",
  showIcon = true,
  ...props
}: AskAIButtonProps) {
  const { toggleSidebar, isOpen } = useAIPane();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      toggleSidebar();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    // Handle Enter and Space key presses for better accessibility
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleClick();
    }
  };
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={variant}
          size={size}
          disabled={disabled}
          onClick={handleClick}
          onKeyDown={handleKeyDown}
          className={cn(
            // Base styles for the Ask AI button
            "relative group transition-all duration-200",
            // Hover effects
            "hover:shadow-md hover:scale-[1.02]",
            // Focus styles - Enhanced for WCAG 2.2 compliance
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background",
            // Disabled styles
            "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none",
            // Dark mode support
            "dark:hover:shadow-lg dark:hover:shadow-primary/10",
            // Active state when AI pane is open
            isOpen && "bg-primary/10 border-primary/20 shadow-sm",
            className,
          )}
          aria-label={tooltipText}
          aria-expanded={isOpen}
          aria-pressed={isOpen}
          {...props}
        >
          <div className="flex items-center gap-2">
            {showIcon && (
              <Sparkles
                className={cn(
                  "transition-transform duration-200 group-hover:rotate-12",
                  size === "sm" ? "h-3 w-3" : size === "lg" ? "h-5 w-5" : "h-4 w-4",
                )}
                aria-hidden="true"
              />
            )}
            <span className="font-medium">Ask AI</span>
          </div>

          {/* Subtle glow effect on hover */}
          <div
            className={cn(
              "absolute inset-0 rounded-md opacity-0 transition-opacity duration-200",
              "group-hover:opacity-100 bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5",
              "dark:from-primary/10 dark:via-primary/20 dark:to-primary/10",
              disabled && "group-hover:opacity-0",
            )}
            aria-hidden="true"
          />
        </Button>
      </TooltipTrigger>
      <TooltipContent side="bottom" sideOffset={8}>
        <p>{tooltipText}</p>
      </TooltipContent>
    </Tooltip>
  );
}
