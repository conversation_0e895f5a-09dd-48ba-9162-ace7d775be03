"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { X, Building2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface CreateOrganizationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit?: (organizationName: string) => void;
  isLoading?: boolean;
}

export function CreateOrganizationDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
}: CreateOrganizationDialogProps) {
  const [organizationName, setOrganizationName] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!organizationName.trim()) {
      setError("Organization name is required");
      return;
    }

    if (organizationName.trim().length < 2) {
      setError("Organization name must be at least 2 characters");
      return;
    }

    if (organizationName.trim().length > 100) {
      setError("Organization name must be less than 100 characters");
      return;
    }

    onSubmit?.(organizationName.trim());
  };

  const handleClose = () => {
    setOrganizationName("");
    setError("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px] p-0 [&>button]:hidden">
        <DialogTitle className="sr-only">Create New Organization</DialogTitle>
        <Card className="border-0 shadow-none">
          <CardHeader className="flex flex-row items-start space-y-0 pb-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Building2 className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold">Create New Organization</CardTitle>
                <DialogDescription className="text-sm text-muted-foreground mt-1">
                  Create a new organization to manage documents and collaborate with your team.
                </DialogDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 ml-auto hover:bg-accent"
              onClick={handleClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="organization-name" className="text-sm font-medium">
                  Organization Name
                </Label>
                <Input
                  id="organization-name"
                  type="text"
                  placeholder="e.g., Your Organization"
                  value={organizationName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setOrganizationName(e.target.value);
                    if (error) setError("");
                  }}
                  className={cn(
                    error && "border-destructive focus:border-destructive focus:ring-destructive",
                  )}
                  disabled={isLoading}
                />
                {error && <p className="text-sm text-destructive">{error}</p>}
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading || !organizationName.trim()}>
                  {isLoading ? "Creating..." : "Create Organization"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
