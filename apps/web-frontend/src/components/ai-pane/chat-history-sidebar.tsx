"use client";

import React from "react";
import { Plus, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ChatHistorySidebarProps {
  isVisible: boolean;
  onToggle: () => void;
  showCloseButton?: boolean;
}

interface ChatHistoryItem {
  id: string;
  title: string;
  isActive: boolean;
  timestamp: string;
}

// Mock chat history data - will be replaced with real data in Phase 3
const mockChatHistory: ChatHistoryItem[] = [
  {
    id: "1",
    title: "What is cost-sharing reductions",
    isActive: true,
    timestamp: "2 hours ago",
  },
  {
    id: "2",
    title: "How do I know if a treatment is covered",
    isActive: false,
    timestamp: "1 day ago",
  },
  {
    id: "3",
    title: "What is emergency room services",
    isActive: false,
    timestamp: "2 days ago",
  },
  {
    id: "4",
    title: "What is in-network copayment",
    isActive: false,
    timestamp: "3 days ago",
  },
  {
    id: "5",
    title: "What is out-of-network copayment",
    isActive: false,
    timestamp: "4 days ago",
  },
  {
    id: "6",
    title: "What is out-of-network deductible",
    isActive: false,
    timestamp: "5 days ago",
  },
  {
    id: "7",
    title: "What is out-of-network coinsurance",
    isActive: false,
    timestamp: "6 days ago",
  },
  {
    id: "8",
    title: "What is out-of-network coinsurance",
    isActive: false,
    timestamp: "7 days ago",
  },
  {
    id: "9",
    title: "What is out-of-network coinsurance",
    isActive: false,
    timestamp: "8 days ago",
  },
  {
    id: "10",
    title: "What is out-of-network coinsurance",
    isActive: false,
    timestamp: "9 days ago",
  },
  {
    id: "11",
    title: "What is out-of-network coinsurance",
    isActive: false,
    timestamp: "10 days ago",
  },
];

export function ChatHistorySidebar({
  isVisible,
  onToggle: _onToggle,
  showCloseButton: _showCloseButton = false,
}: ChatHistorySidebarProps) {
  if (!isVisible) return null;
  return (
    <div className="w-64 sm:w-64 md:w-72 lg:w-80 bg-card border-r border-border flex flex-col min-w-0 max-w-[280px] sm:max-w-none h-full min-h-0">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-foreground">Chat History</h3>
          {/* {showCloseButton && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                console.log("ChatHistorySidebar close button clicked");
                onToggle();
              }}
              className="h-6 w-6 text-muted-foreground hover:text-foreground hover:bg-accent"
            >
              <X className="h-4 w-4" />
            </Button>
          )} */}
        </div>

        {/* New Chat Button */}
        <Button variant="default" size="sm" className="w-full justify-start gap-2">
          <Plus className="h-4 w-4" />
          Start New Chat
        </Button>
      </div>

      {/* Search */}
      <div className="flex-shrink-0 p-4 border-b border-border">
        <div className="relative">
          <input
            type="text"
            placeholder="Search conversations..."
            className="w-full px-3 py-2 text-sm bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
          />
        </div>
      </div>

      {/* Chat History List */}
      <div className="overflow-y-auto" style={{ height: "calc(100vh - 240px)" }}>
        <div className="p-2 space-y-1 pb-4">
          {mockChatHistory.map((chat) => (
            <div
              key={chat.id}
              className={cn(
                "group relative flex items-start gap-3 p-3 rounded-lg cursor-pointer transition-colors",
                chat.isActive
                  ? "bg-accent text-accent-foreground"
                  : "text-muted-foreground hover:bg-accent/50 hover:text-foreground",
              )}
            >
              <div className="flex-shrink-0 mt-0.5">
                <MessageSquare className="h-4 w-4" />
              </div>

              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{chat.title}</p>
                <p className="text-xs text-muted-foreground mt-1">{chat.timestamp}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Empty state */}
        {mockChatHistory.length === 0 && (
          <div className="p-8 text-center">
            <MessageSquare className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
            <p className="text-sm text-muted-foreground">No conversations yet</p>
            <p className="text-xs text-muted-foreground/70 mt-1">Start your first conversation</p>
          </div>
        )}
      </div>
    </div>
  );
}
