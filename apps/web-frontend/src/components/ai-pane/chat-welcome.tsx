"use client";

import React from "react";
import { Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface ChatWelcomeProps {
  onSuggestedQuestion: (question: string) => void;
}

// GRC-focused suggested questions (top 4)
const suggestedQuestions = [
  "Show our current compliance status for SOC 2 and ISO 27001",
  "List open high-risk findings with owners and due dates",
  "Which controls are missing evidence for this quarter?",
  "Which vendors are overdue on security questionnaires or reviews?",
];

export function ChatWelcome({ onSuggestedQuestion }: ChatWelcomeProps) {
  return (
    <div className="flex flex-col items-center justify-center p-4 text-center min-h-full">
      {/* AI Assistant Icon */}
      <div className="w-12 h-12 rounded-full bg-primary flex items-center justify-center mb-4">
        <Sparkles className="h-6 w-6 text-primary-foreground" />
      </div>

      {/* Welcome Message */}
      <div className="mb-4 max-w-sm">
        <h2 className="text-base font-semibold text-foreground mb-2 leading-tight">
          Welcome to Anter AI! I'm here to help you analyze, summarize, and find information in your
          organization's documents.
        </h2>
      </div>

      {/* Suggested Questions */}
      <div className="w-full max-w-sm sm:max-w-md space-y-2">
        <div className="flex flex-col gap-2">
          {suggestedQuestions.map((question, index) => (
            <Button
              key={index}
              variant="outline"
              onClick={() => onSuggestedQuestion(question)}
              className="h-auto w-full p-2.5 text-left justify-start transition-colors whitespace-normal break-words min-h-[40px] touch-manipulation"
            >
              <span className="block text-sm font-medium leading-relaxed">{question}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Additional Info */}
      <div className="mt-4 text-xs text-muted-foreground max-w-sm">
        <p>
          You can ask me anything about your documents, and I'll help you find the information you
          need.
        </p>
      </div>
    </div>
  );
}
