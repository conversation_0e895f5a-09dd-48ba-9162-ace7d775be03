"use client";

import React from "react";
import { X, Maximize2, Minimize2, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface AIPaneHeaderProps {
  onClose: () => void;
  onToggle: () => void;
  onClear?: () => void;
  isFullscreen?: boolean;
  className?: string;
  leftElement?: React.ReactNode;
}

export function AIPaneHeader({
  onClose,
  onToggle,
  onClear,
  isFullscreen = false,
  className,
  leftElement,
}: AIPaneHeaderProps) {
  return (
    <div
      className={`flex items-center justify-between p-2 border-b bg-background/95 backdrop-blur-sm ${className || ""}`}
    >
      <div className="flex items-center gap-2">
        {leftElement}
        <div className="p-1.5 rounded-lg bg-primary/10">
          <Sparkles className="h-4 w-4 text-primary" />
        </div>
        <div>
          <h2 id="ai-pane-title" className="text-md font-semibold">
            AI Assistant
          </h2>
        </div>
      </div>

      <div className="flex items-center gap-1">
        {/* Toggle button (Maximize/Minimize) */}
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className="h-8 w-8 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-md transition-colors"
          aria-label={isFullscreen ? "Minimize to sidebar" : "Maximize to full screen"}
          type="button"
        >
          {isFullscreen ? (
            <Minimize2 className="h-4 w-4" aria-hidden="true" />
          ) : (
            <Maximize2 className="h-4 w-4" aria-hidden="true" />
          )}
        </Button>

        {/* Clear/Reset button */}
        {onClear && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onClear}
            className="h-8 w-8 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-md transition-colors"
            aria-label="Clear conversation"
            type="button"
          >
            <Trash2 className="h-4 w-4" aria-hidden="true" />
          </Button>
        )}

        {/* Close button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="h-8 w-8 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-md transition-colors"
          aria-label="Close AI pane"
          type="button"
        >
          <X className="h-4 w-4" aria-hidden="true" />
        </Button>
      </div>
    </div>
  );
}
