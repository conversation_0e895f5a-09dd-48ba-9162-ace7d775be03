"use client";

import React, { useEffect } from "react";
import { cn } from "@/lib/utils";
import { useAIPane } from "@/providers/ai-pane-provider";
import { useAIAssistantContext } from "@/providers/ai-assistant-provider";
import { AIPaneHeader } from "./ai-pane-header";
import { AIPaneChatContent } from "./ai-pane-chat-content";
import { AIPaneChatInput } from "./ai-pane-chat-input";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface AIPaneSidebarProps {
  className?: string;
}

export function AIPaneSidebar({ className }: AIPaneSidebarProps) {
  const { mode, close, openFullscreen } = useAIPane();
  const isOpen = mode === "sidebar";

  // Use the AI assistant hook
  const { messages, isStreaming, isLoading, error, sendMessage, clearMessages, retryLastMessage } =
    useAIAssistantContext();

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        close();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when pane is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, close]);

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage(message);
    } catch (error) {
      console.error("Error sending message:", error);
      // Error handling is managed by the hook
    }
  };

  const handleSuggestedQuestion = (question: string) => {
    handleSendMessage(question);
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop overlay */}
      <div
        className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300"
        onClick={close}
        aria-hidden="true"
      />

      {/* AI Pane Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 right-0 z-50 w-full sm:w-[400px] bg-background border-l shadow-xl",
          "transform transition-transform duration-300 ease-in-out",
          "flex flex-col",
          isOpen ? "translate-x-0" : "translate-x-full",
          className,
        )}
        role="dialog"
        aria-modal="true"
        aria-labelledby="ai-pane-title"
      >
        {/* Header */}
        <AIPaneHeader
          onClose={close}
          onToggle={openFullscreen}
          onClear={() => clearMessages()}
          isFullscreen={false}
        />

        {/* Global Error Display */}
        {error && (
          <div className="mx-4 mb-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-4 h-4 text-destructive mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm text-destructive font-medium">Connection Error</p>
                <p className="text-xs text-destructive/80 mt-1">{error}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={retryLastMessage}
                className="h-6 px-2 text-xs text-destructive hover:text-destructive"
              >
                Retry
              </Button>
            </div>
          </div>
        )}

        {/* Chat Content */}
        <AIPaneChatContent
          messages={messages}
          isStreaming={isStreaming}
          onSuggestedQuestion={handleSuggestedQuestion}
          onRetry={retryLastMessage}
        />

        {/* Chat Input */}
        <AIPaneChatInput onSendMessage={handleSendMessage} disabled={isLoading || isStreaming} />
      </div>
    </>
  );
}
