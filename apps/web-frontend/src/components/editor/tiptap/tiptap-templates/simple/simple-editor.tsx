"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import { useEffect, useRef } from "react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import TextAlign from "@tiptap/extension-text-align";
import { Button } from "@/components/ui/button";
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo,
  Strikethrough,
  Code,
  Quote,
} from "lucide-react";

// --- Styles ---
import "./simple-editor.scss";

export const SimpleEditor = ({
  content,
  onSave,
  onCancel: _onCancel,
  triggerSave,
  canEdit = true,
  onDirtyChange,
  sessionId,
}: {
  content: string;
  onSave?: (content: string) => void;
  onCancel?: () => void;
  triggerSave?: boolean;
  canEdit?: boolean;
  onDirtyChange?: (isDirty: boolean) => void;
  sessionId?: number;
}) => {
  // Track initial content to compute dirty state
  const initialContentRef = useRef<string>(content || "");
  const lastDirtyRef = useRef<boolean>(false);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
    ],
    content: content || "",
    editorProps: {
      attributes: {
        class: "prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none h-full",
      },
    },
    immediatelyRender: false,
    editable: canEdit,
    onUpdate: ({ editor }) => {
      if (!onDirtyChange) return;
      const html = editor.getHTML();
      const isDirty = html !== initialContentRef.current;
      if (lastDirtyRef.current !== isDirty) {
        lastDirtyRef.current = isDirty;
        onDirtyChange(isDirty);
      }
    },
  });

  const handleSave = () => {
    if (!editor) return;
    const editorContent = editor.getHTML();
    console.log("SimpleEditor: Saving content:", editorContent);
    onSave?.(editorContent);
  };

  // Trigger save when triggerSave prop changes
  useEffect(() => {
    if (triggerSave && editor) {
      handleSave();
    }
  }, [triggerSave, editor]);

  // Auto-focus the editor when it's ready
  useEffect(() => {
    if (editor && canEdit) {
      // Small delay to ensure the editor is fully rendered
      const timer = setTimeout(() => {
        editor.commands.focus();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [editor, canEdit]);

  // Reset baseline (and content) when a new session starts or content prop changes
  useEffect(() => {
    if (!editor) return;
    editor.commands.setContent(content || "");
    const html = editor.getHTML();
    initialContentRef.current = html;
    lastDirtyRef.current = false;
    onDirtyChange?.(false);
  }, [sessionId, editor, content]);

  if (!editor) {
    return null;
  }

  return (
    <div className="simple-editor">
      <div className="editor-container">
        {/* Toolbar */}
        <div className="toolbar">
          <div className="toolbar-group">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={editor.isActive("bold") ? "bg-gray-200 dark:bg-gray-700" : ""}
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={editor.isActive("italic") ? "bg-gray-200 dark:bg-gray-700" : ""}
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className={editor.isActive("underline") ? "bg-gray-200 dark:bg-gray-700" : ""}
            >
              <UnderlineIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleStrike().run()}
              className={editor.isActive("strike") ? "bg-gray-200 dark:bg-gray-700" : ""}
            >
              <Strikethrough className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleCode().run()}
              className={editor.isActive("code") ? "bg-gray-200 dark:bg-gray-700" : ""}
            >
              <Code className="h-4 w-4" />
            </Button>
          </div>

          <div className="toolbar-group">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
              className={
                editor.isActive("heading", { level: 1 }) ? "bg-gray-200 dark:bg-gray-700" : ""
              }
            >
              <Heading1 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
              className={
                editor.isActive("heading", { level: 2 }) ? "bg-gray-200 dark:bg-gray-700" : ""
              }
            >
              <Heading2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
              className={
                editor.isActive("heading", { level: 3 }) ? "bg-gray-200 dark:bg-gray-700" : ""
              }
            >
              <Heading3 className="h-4 w-4" />
            </Button>
          </div>

          <div className="toolbar-group">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={editor.isActive("bulletList") ? "bg-gray-200 dark:bg-gray-700" : ""}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={editor.isActive("orderedList") ? "bg-gray-200 dark:bg-gray-700" : ""}
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              className={editor.isActive("blockquote") ? "bg-gray-200 dark:bg-gray-700" : ""}
            >
              <Quote className="h-4 w-4" />
            </Button>
          </div>

          <div className="toolbar-group">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().setTextAlign("left").run()}
              className={
                editor.isActive({ textAlign: "left" }) ? "bg-gray-200 dark:bg-gray-700" : ""
              }
            >
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().setTextAlign("center").run()}
              className={
                editor.isActive({ textAlign: "center" }) ? "bg-gray-200 dark:bg-gray-700" : ""
              }
            >
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().setTextAlign("right").run()}
              className={
                editor.isActive({ textAlign: "right" }) ? "bg-gray-200 dark:bg-gray-700" : ""
              }
            >
              <AlignRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="toolbar-group">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Editor Content with proper scroll wrapper */}
        <div className="content-wrapper">
          <EditorContent editor={editor} />
        </div>
      </div>
    </div>
  );
};
