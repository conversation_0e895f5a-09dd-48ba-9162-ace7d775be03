"use client";

import React from "react";

interface PageHeaderProps {
  headerTitle?: string;
  headerDescription?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({ headerTitle, headerDescription }) => {
  return (
    <div className="bg-background sticky top-12 z-30 px-4 lg:px-6 transition-colors duration-200 flex-shrink-0">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          {headerTitle && <h1 className="text-2xl font-bold text-foreground">{headerTitle}</h1>}
          {headerDescription && <p className="text-muted-foreground mt-1">{headerDescription}</p>}
        </div>
      </div>
    </div>
  );
};
