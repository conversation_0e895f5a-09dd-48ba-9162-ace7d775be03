"use client";

import React, { useState, useRef } from "react";
import { useOrganization } from "@/providers/organization-provider";
import { useRouter } from "next/navigation";
import { switchOrganization } from "@/actions/organizations";
import { useCreateOrganization } from "@/hooks/use-create-organization";
import { ChevronDown, Search, Plus, Check, Building2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CreateOrganizationDialog } from "@/components/create-organization-dialog";
import { cn } from "@/lib/utils";

export function OrganizationSwitcher() {
  const {
    organizations,
    currentOrganizationId,
    setCurrentOrganizationId,
    isLoading: orgLoading,
  } = useOrganization();
  const router = useRouter();
  const { createOrganization, isLoading: createLoading } = useCreateOrganization();

  // Debug logging
  // console.debug("[OrganizationSwitcher] State:", {
  //   organizations,
  //   currentOrganizationId,
  //   orgLoading,
  //   organizationsLength: organizations.length,
  //   firstOrganization: organizations[0],
  //   allOrganizations: organizations.map((org) => ({
  //     id: org.id,
  //     companyName: org.companyName,
  //   })),
  // });

  const [isSwitching, setIsSwitching] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const triggerButtonRef = useRef<HTMLButtonElement>(null);

  // Removed forced focus on trigger when popover opens – it interferes with
  // focusing the search input using the mouse.

  const handleOrganizationSwitch = async (organizationId: string) => {
    if (isSwitching || organizationId === currentOrganizationId) {
      return;
    }

    setIsSwitching(true);

    try {
      const result = await switchOrganization(organizationId);

      if (result.success) {
        // Update the organization context
        setCurrentOrganizationId(organizationId);

        // Close the popover
        setIsPopoverOpen(false);

        // Navigate to the new organization URL
        router.push(`/${encodeURIComponent(organizationId)}/`);
      } else {
        console.error("Failed to switch organization:", result.error);
      }
    } catch (error) {
      console.error("Error switching organization:", error);
    } finally {
      setIsSwitching(false);
    }
  };

  const handleCreateOrganization = () => {
    setShowCreateDialog(true);
  };

  const handleCreateOrganizationSubmit = async (organizationName: string) => {
    try {
      const result = await createOrganization({
        companyName: organizationName,
      });
      if (result) {
        setShowCreateDialog(false);
        // The organization context will be updated automatically by the hook
        // and the user will be redirected to the new organization
        // No need to manually update context here as the hook handles it
      }
    } catch (error) {
      console.error("Failed to create organization:", error);
      // Keep dialog open on error so user can try again
    }
  };

  const handlePopoverOpenChange = (open: boolean) => {
    // Add a small delay to prevent immediate closing on smaller screens
    if (open) {
      setIsPopoverOpen(true);
    } else {
      // Clear search when popover closes
      setSearchQuery("");
      setIsPopoverOpen(false);
    }
  };

  // Filter organizations based on search query
  const filteredOrganizations = organizations.filter(
    (org) =>
      org.companyName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      org.id.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Show loading state while organizations are being fetched
  if (orgLoading) {
    return (
      <Button
        variant="ghost"
        size="sm"
        className="h-9 px-3 text-sm font-medium text-muted-foreground"
        disabled
      >
        <Building2 className="w-4 h-4 mr-2" />
        <span className="truncate max-w-32">Loading...</span>
        <ChevronDown className="w-4 h-4 ml-2" />
      </Button>
    );
  }

  // Don't render if no organizations and not loading
  if (organizations.length === 0 && !currentOrganizationId) {
    return null;
  }

  const currentOrg = organizations.find((org) => org.id === currentOrganizationId) || {
    id: currentOrganizationId!,
    companyName:
      organizations.find((o) => o.id === currentOrganizationId)?.companyName || "Organization",
  };

  return (
    <>
      <Popover open={isPopoverOpen} onOpenChange={handlePopoverOpenChange}>
        <PopoverTrigger asChild>
          <Button
            ref={triggerButtonRef}
            variant="ghost"
            size="sm"
            className="h-9 px-3 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-accent transition-colors duration-200"
            aria-label={`Current organization: ${currentOrg?.companyName || "Select Organization"}. Click to switch organizations.`}
            aria-expanded={isPopoverOpen}
            aria-haspopup="true"
          >
            <Building2 className="w-4 h-4 mr-2" aria-hidden="true" />
            <span className="truncate max-w-32">
              {currentOrg?.companyName || "Select Organization"}
            </span>
            <ChevronDown className="w-4 h-4 ml-2" aria-hidden="true" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-80 p-0 z-[100]"
          sideOffset={8}
          align="start"
          side="bottom"
          avoidCollisions={true}
          collisionPadding={8}
        >
          <div className="p-4 border-b border-border">
            <div className="flex items-center gap-2 mb-3">
              <Building2 className="w-5 h-5 text-muted-foreground" />
              <h3 className="text-sm font-semibold text-foreground">Organizations</h3>
            </div>
            <div className="relative">
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
                aria-hidden="true"
              />
              <Input
                placeholder="Search organizations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 border-primary/20"
                aria-label="Search organizations"
                autoComplete="off"
              />
            </div>
          </div>

          {/* Organizations List */}
          <div className="px-3 pb-3">
            <div className="max-h-48 overflow-y-auto space-y-0.5">
              {filteredOrganizations.map((org) => (
                <div key={org.id} className="py-0.5">
                  <button
                    onClick={() => handleOrganizationSwitch(org.id)}
                    disabled={isSwitching}
                    className={cn(
                      "w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-all duration-200 group h-10",
                      org.id === currentOrganizationId
                        ? "bg-primary/10 text-foreground border border-primary/20 shadow-sm"
                        : "text-foreground hover:bg-accent hover:text-accent-foreground",
                      isSwitching && "opacity-50 cursor-not-allowed",
                    )}
                    aria-label={`Switch to ${org.companyName} organization${org.id === currentOrganizationId ? " (currently selected)" : ""}`}
                    aria-current={org.id === currentOrganizationId ? "true" : "false"}
                    type="button"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          "w-6 h-6 rounded flex items-center justify-center text-xs font-bold shadow-sm transition-all duration-200",
                          org.id === currentOrganizationId
                            ? "bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-primary/20"
                            : "bg-gradient-to-br from-muted to-muted/80 text-muted-foreground group-hover:from-primary/60 group-hover:to-primary/80 group-hover:text-primary-foreground",
                        )}
                      >
                        {org.companyName.charAt(0).toUpperCase()}
                      </div>
                      <span className="truncate font-medium">{org.companyName}</span>
                    </div>
                    {org.id === currentOrganizationId && <Check className="w-4 h-4 text-primary" />}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* New Organization Button */}
          <div className="px-3 py-2 border-t border-border">
            <Button
              onClick={handleCreateOrganization}
              variant="ghost"
              size="sm"
              className="w-full justify-start text-sm text-muted-foreground hover:text-foreground hover:bg-accent py-2.5"
              aria-label="Create new organization"
            >
              <Plus className="w-4 h-4 mr-2" aria-hidden="true" />
              New Organization
            </Button>
          </div>
        </PopoverContent>
      </Popover>

      {/* Create Organization Dialog */}
      <CreateOrganizationDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleCreateOrganizationSubmit}
        isLoading={createLoading}
      />
    </>
  );
}
