"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "./ui/alert-dialog";
import { Trash2, AlertTriangle } from "lucide-react";

interface Entity {
  id: string;
  name: string;
}

interface ConfirmDeleteProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  entities: Entity[];
  entityType: string; // e.g., "file", "document", "user"
  isLoading?: boolean;
}

export function ConfirmDelete({
  isOpen,
  onClose,
  onConfirm,
  entities,
  entityType,
  isLoading = false,
}: ConfirmDeleteProps) {
  const isMultiple = entities.length > 1;
  const entityName = isMultiple ? `${entityType}s` : entityType;

  return (
    <AlertDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
              <AlertTriangle className="h-5 w-5 text-destructive" />
            </div>
            <div>
              <AlertDialogTitle className="text-left">Delete {entityName}</AlertDialogTitle>
              <AlertDialogDescription className="text-left text-muted-foreground">
                This action cannot be undone.
              </AlertDialogDescription>
            </div>
          </div>
        </AlertDialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
            <div className="flex items-start gap-3">
              <Trash2 className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <p className="text-sm font-medium text-destructive">
                  {isMultiple
                    ? `You are about to delete ${entities.length} ${entityName.toLowerCase()}.`
                    : `You are about to delete this ${entityType.toLowerCase()}.`}
                </p>
                <div className="space-y-1">
                  {entities.slice(0, 3).map((entity) => (
                    <p key={entity.id} className="text-sm text-muted-foreground">
                      • {entity.name}
                    </p>
                  ))}
                  {entities.length > 3 && (
                    <p className="text-sm text-muted-foreground">
                      • and {entities.length - 3} more...
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <AlertDialogFooter className="gap-2 sm:gap-0">
          <AlertDialogCancel onClick={onClose} disabled={isLoading} className="order-2 sm:order-1">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className="order-1 sm:order-2 bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Deleting...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Trash2 className="h-4 w-4" />
                Delete {isMultiple ? "All" : entityType}
              </div>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
