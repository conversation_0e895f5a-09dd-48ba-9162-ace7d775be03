import { useState, useCallback } from "react";

interface Entity {
  id: string;
  name: string;
}

interface UseConfirmDeleteReturn {
  isOpen: boolean;
  entities: Entity[];
  entityType: string;
  openDialog: (entities: Entity[], entityType: string) => void;
  closeDialog: () => void;
}

export function useConfirmDelete(): UseConfirmDeleteReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [entities, setEntities] = useState<Entity[]>([]);
  const [entityType, setEntityType] = useState("");

  const openDialog = useCallback((entities: Entity[], entityType: string) => {
    setEntities(entities);
    setEntityType(entityType);
    setIsOpen(true);
  }, []);

  const closeDialog = useCallback(() => {
    setIsOpen(false);
    setEntities([]);
    setEntityType("");
  }, []);

  return {
    isOpen,
    entities,
    entityType,
    openDialog,
    closeDialog,
  };
}
