/**
 * Content extraction utilities that preserve formatting for different file types
 * The extracted content is designed to be compatible with Tiptap editor (HTML format)
 */

import { renderAsync } from "docx-preview";

export interface ContentExtractionResult {
  success: boolean;
  content?: string; // HTML content for Tiptap editor
  error?: string;
}

/**
 * Extract formatted content from a file based on its type
 * Returns HTML content that can be used directly in Tiptap editor
 */
export async function extractFormattedContent(
  file: File,
  fileExtension: string,
): Promise<ContentExtractionResult> {
  try {
    switch (fileExtension.toLowerCase()) {
      case "html":
      case "htm":
        return await extractHtmlContent(file);

      case "md":
      case "markdown":
        return await extractMarkdownContent(file);

      case "docx":
        return await extractDocxContent(file);

      case "txt":
        return await extractTxtContent(file);

      default:
        // Fallback to plain text extraction for unknown types
        return await extractTxtContent(file);
    }
  } catch (error) {
    console.error("Content extraction error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown extraction error",
    };
  }
}

/**
 * Extract HTML content (already in HTML format)
 */
async function extractHtmlContent(file: File): Promise<ContentExtractionResult> {
  try {
    // Convert to Blob for consistent processing pipeline and explicit MIME type control
    const arrayBuffer = await file.arrayBuffer();
    const blob = new Blob([arrayBuffer], { type: file.type || "text/html" });
    const htmlContent = await blob.text();

    // Basic sanitization - remove DOCTYPE, html, head, body tags
    // Keep only the content that should go into the editor
    const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    const content = bodyMatch ? bodyMatch[1] : htmlContent;

    // Remove script tags for security
    const sanitized = content?.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "");

    return {
      success: true,
      content: sanitized?.trim(),
    };
  } catch (error) {
    return {
      success: false,
      error: `Failed to extract HTML content: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Extract and convert markdown content to HTML
 */
async function extractMarkdownContent(file: File): Promise<ContentExtractionResult> {
  try {
    // Convert to Blob for consistent processing pipeline and explicit MIME type control
    const arrayBuffer = await file.arrayBuffer();
    const blob = new Blob([arrayBuffer], { type: file.type || "text/markdown" });
    const markdownText = await blob.text();

    // Simple markdown to HTML converter
    // This handles the most common markdown syntax
    const htmlContent = convertMarkdownToHtml(markdownText);

    return {
      success: true,
      content: htmlContent,
    };
  } catch (error) {
    return {
      success: false,
      error: `Failed to extract markdown content: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Extract DOCX content as HTML using docx-preview library
 */
async function extractDocxContent(file: File): Promise<ContentExtractionResult> {
  try {
    // Create a hidden container element for rendering
    const hiddenContainer = document.createElement("div");
    hiddenContainer.style.position = "absolute";
    hiddenContainer.style.left = "-9999px";
    hiddenContainer.style.top = "-9999px";
    hiddenContainer.style.width = "1px";
    hiddenContainer.style.height = "1px";
    hiddenContainer.style.overflow = "hidden";
    document.body.appendChild(hiddenContainer);

    try {
      // Convert File to ArrayBuffer then to Blob
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const blob = new Blob([uint8Array], {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      });

      // Render DOCX to HTML using docx-preview
      await renderAsync(blob, hiddenContainer, hiddenContainer, {
        ignoreWidth: true,
        ignoreHeight: true,
        ignoreFonts: false,
        breakPages: false, // Don't break pages for editor content
        debug: false,
      });

      // Extract the rendered HTML content
      const htmlContent = hiddenContainer.innerHTML;

      if (!htmlContent.trim()) {
        throw new Error("No content extracted from DOCX file");
      }

      return {
        success: true,
        content: htmlContent,
      };
    } finally {
      // Clean up the hidden container
      document.body.removeChild(hiddenContainer);
    }
  } catch (error) {
    return {
      success: false,
      error: `Failed to extract DOCX content: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Extract plain text content and convert to basic HTML
 */
async function extractTxtContent(file: File): Promise<ContentExtractionResult> {
  try {
    // Convert to Blob for consistent processing pipeline and explicit MIME type control
    const arrayBuffer = await file.arrayBuffer();
    const blob = new Blob([arrayBuffer], { type: file.type || "text/plain" });
    const textContent = await blob.text();

    // Convert plain text to HTML with proper paragraph handling
    const htmlContent = convertTextToHtml(textContent);

    return {
      success: true,
      content: htmlContent,
    };
  } catch (error) {
    return {
      success: false,
      error: `Failed to extract text content: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Simple markdown to HTML converter
 * Handles the most common markdown syntax
 */
function convertMarkdownToHtml(markdown: string): string {
  let html = markdown
    // Headers
    .replace(/^### (.*$)/gm, "<h3>$1</h3>")
    .replace(/^## (.*$)/gm, "<h2>$1</h2>")
    .replace(/^# (.*$)/gm, "<h1>$1</h1>")

    // Bold and italic
    .replace(/\*\*\*(.*?)\*\*\*/g, "<strong><em>$1</em></strong>")
    .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
    .replace(/\*(.*?)\*/g, "<em>$1</em>")

    // Code blocks (simple)
    .replace(/```[\s\S]*?```/g, (match) => {
      const content = match.slice(3, -3).trim();
      return `<pre><code>${content}</code></pre>`;
    })

    // Inline code
    .replace(/`(.*?)`/g, "<code>$1</code>")

    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')

    // Line breaks and paragraphs
    .replace(/\n\n/g, "</p><p>")
    .replace(/\n/g, "<br>");

  // Wrap in paragraphs if there's content
  if (html.trim()) {
    // Only wrap if not already wrapped in block elements
    if (!html.match(/^<(h[1-6]|p|div|pre|ul|ol|blockquote)/)) {
      html = `<p>${html}</p>`;
    }
  }

  return html;
}

/**
 * Convert plain text to HTML with proper formatting
 */
function convertTextToHtml(text: string): string {
  if (!text.trim()) {
    return "";
  }

  // Split by double line breaks to create paragraphs
  const paragraphs = text.split(/\n\s*\n/);

  const htmlParagraphs = paragraphs
    .map((paragraph) => paragraph.trim())
    .filter((paragraph) => paragraph.length > 0)
    .map((paragraph) => {
      // Replace single line breaks with <br> tags within paragraphs
      const withBreaks = paragraph.replace(/\n/g, "<br>");
      return `<p>${withBreaks}</p>`;
    });

  return htmlParagraphs.join("");
}

/**
 * Check if content extraction is supported for the given file
 */
export function isContentExtractionSupported(fileName: string): boolean {
  const extension = fileName.split(".").pop()?.toLowerCase();
  return ["html", "htm", "md", "markdown", "docx", "txt"].includes(extension || "");
}
