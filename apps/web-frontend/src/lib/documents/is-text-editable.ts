export interface TextEditabilityInput {
  name?: string | null;
  fileType?: string | null; // MIME type if available
  content?: string | null;
  bufferFile?: string | null; // presence implies uploaded binary
}

function getExtension(name?: string | null): string | null {
  if (!name) return null;
  const idx = name.lastIndexOf(".");
  if (idx < 0 || idx === name.length - 1) return null;
  return name.slice(idx + 1).toLowerCase();
}

function normalizeMime(mime?: string | null): string | null {
  if (!mime) return null;
  return mime.toLowerCase();
}

const ALLOW_EXT = new Set(["md", "txt", "html", "doc", "docx", "pdf"]);

const BLOCK_EXT = new Set([
  "xlsx",
  "xls",
  "ppt",
  "pptx",
  "csv",
  "zip",
  "rar",
  "7z",
  "gz",
  "tar",
  // images
  "png",
  "jpg",
  "jpeg",
  "gif",
  "bmp",
  "webp",
  "svg",
  // audio/video
  "mp3",
  "wav",
  "aac",
  "mp4",
  "webm",
  "mov",
  "avi",
  "mkv",
]);

const TEXT_MIME_PREFIXES = ["text/"];

const ALLOW_MIME_CONTAINS = [
  "markdown",
  "html",
  "xml",
  // Office & PDF
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/pdf",
];

const BLOCK_MIME_PREFIXES = ["image/", "audio/", "video/"];

export function isTextEditable(input: TextEditabilityInput): boolean {
  const ext = getExtension(input.name ?? undefined);
  const mime = normalizeMime(input.fileType ?? undefined);

  if (isBlockedByExt(ext)) return false;
  if (isAllowedByExt(ext)) return true;
  if (isBlockedByMime(mime)) return false;
  if (isAllowedByMime(mime)) return true;
  if (isNonUpload(input.bufferFile)) return true;
  if (hasContent(input.content)) return true;
  return false;
}

export const TextEditability = {
  getExtension,
  normalizeMime,
  isTextEditable,
};

function isNonUpload(bufferFile?: string | null): boolean {
  return !bufferFile;
}

function isBlockedByExt(ext?: string | null): boolean {
  return !!ext && BLOCK_EXT.has(ext);
}

function isAllowedByExt(ext?: string | null): boolean {
  return !!ext && ALLOW_EXT.has(ext);
}

function isBlockedByMime(mime?: string | null): boolean {
  return !!mime && BLOCK_MIME_PREFIXES.some((p) => mime.startsWith(p));
}

function isAllowedByMime(mime?: string | null): boolean {
  if (!mime) return false;
  if (TEXT_MIME_PREFIXES.some((p) => mime.startsWith(p))) return true;
  if (ALLOW_MIME_CONTAINS.some((m) => mime.includes(m))) return true;
  return false;
}

function hasContent(content?: string | null): boolean {
  return !!content && content.trim().length > 0;
}
