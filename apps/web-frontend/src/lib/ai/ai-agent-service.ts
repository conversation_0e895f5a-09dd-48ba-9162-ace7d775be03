import * as https from "https";
import * as http from "http";

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface RequestContext {
  requestId: string;
  traceId: string;
  spanId: string;
  userId?: string;
  organizationId?: string;
  sessionId: string;
  userAgent?: string;
  timestamp: number;
}

export interface AgentInvokeConfig {
  baseUrl: string;
  internalSecret: string;
  timeout: number;
  retries: number;
  sslVerify: boolean;
}

export interface AgentInvokeRequest {
  agent_name: string;
  input:
    | string
    | {
        query: string;
        text: string;
        message: string;
        [key: string]: unknown;
      };
  context: {
    sessionId: string;
    organizationId: string;
    userId: string;
    metadata: {
      source: string;
      timestamp: string;
      query_type: string;
      email?: string;
      user_role?: string;
      [key: string]: unknown;
    };
    requestId: string;
    timestamp: string;
    platform: string;
    channel?: {
      id: string;
      name?: string;
      type: string;
    };
    [key: string]: unknown;
  };
}

export interface AgentSource {
  id: string;
  title: string;
  type: "document" | "file" | "external";
  url?: string;
  confidence: number;
}

export interface AgentMetadata {
  tokensUsed: number;
  processingTime: number;
  model: string;
}

export interface AgentStreamChunk {
  content: string;
  isComplete: boolean;
  sources?: AgentSource[];
  metadata?: AgentMetadata;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

// Enhanced types for advanced response processing
export interface ConfidenceData {
  score: number;
  reason: string;
  isHighConfidence: boolean;
}

export interface ConfidenceInfo {
  score: number;
  reason: string;
  isHighConfidence: boolean;
  isLowConfidence: boolean;
  needsReview: boolean;
}

export interface ProcessedDocument {
  id: string;
  content: string;
  relevance: number;
  metadata: Record<string, unknown>;
}

export interface ProcessedSearchResults {
  documents: ProcessedDocument[];
  averageRelevance: number;
  quality: "high" | "medium" | "low";
  count: number;
}

export interface StreamingPerformanceMetrics {
  executionTime: number;
  totalChunks: number;
  averageChunkTime: number;
}

export interface ProcessedStreamingResponse {
  content: string;
  confidence: ConfidenceInfo;
  searchResults: ProcessedSearchResults;
  performance: StreamingPerformanceMetrics;
  metadata: Record<string, unknown>;
}

export interface StreamingProgress {
  type: "content" | "metadata";
  content?: string;
  fullResponse?: string;
  searchResults?: AgentSource[];
  confidence?: ConfidenceData;
  metadata?: Record<string, unknown>;
  chunkIndex: number;
  elapsedMs: number;
}

// Error types
export class AgentServiceError extends Error {
  constructor(
    message: string,
    public readonly statusCode?: number,
    public readonly responseData?: string,
    public readonly originalError?: Error,
  ) {
    super(message);
    this.name = "AgentServiceError";
  }
}

export class AgentValidationError extends Error {
  constructor(
    message: string,
    public readonly field?: string,
    public readonly value?: unknown,
  ) {
    super(message);
    this.name = "AgentValidationError";
  }
}

export class AgentAuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AgentAuthenticationError";
  }
}

export class AgentRateLimitError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AgentRateLimitError";
  }
}

export class AgentNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AgentNotFoundError";
  }
}

export class AgentNotSupportedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AgentNotSupportedError";
  }
}

export class AgentTimeoutError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AgentTimeoutError";
  }
}

export class AgentNetworkError extends Error {
  constructor(
    message: string,
    public readonly originalError: Error,
  ) {
    super(message);
    this.name = "AgentNetworkError";
  }
}

export class AgentStreamingError extends Error {
  constructor(
    message: string,
    public readonly code: string,
  ) {
    super(message);
    this.name = "AgentStreamingError";
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Validates and safely parses JSON data
 */
export function safeJsonParse(data: string): unknown {
  validateJsonInput(data);
  try {
    const parsed = JSON.parse(data);
    validateParsedObject(parsed);
    return parsed;
  } catch (error) {
    if (error instanceof AgentValidationError) throw error;
    throw new AgentValidationError("Invalid JSON format", "data", preview(data));
  }
}

function validateJsonInput(input: unknown): void {
  if (typeof input !== "string") {
    throw new AgentValidationError("Input must be a string", "data", input);
  }
  if (input.length === 0) {
    throw new AgentValidationError("Input cannot be empty", "data", input);
  }
  if (input.length > 10 * 1024 * 1024) {
    // 10MB limit
    throw new AgentValidationError("Input too large", "data", input.length);
  }
}

function validateParsedObject(parsed: unknown): void {
  if (parsed === null || typeof parsed !== "object") {
    throw new AgentValidationError("Parsed data must be an object", "parsed", parsed);
  }
}

function preview(s: string): string {
  return s.substring(0, 100) + (s.length > 100 ? "..." : "");
}

/**
 * Validates and normalizes agent source
 */
export function validateAgentSource(source: unknown): AgentSource {
  ensureObject(source, "source");
  ensureNotArray(source, "source");
  const sourceObj = source as Record<string, unknown>;

  const id = String(sourceObj.id || sourceObj.title || "");
  const title = String(sourceObj.title || sourceObj.id || "");
  const type = normalizeType(sourceObj.type);
  const url = normalizeUrl(sourceObj.url);
  const confidence = normalizeConfidence(sourceObj.confidence);

  return { id, title, type, url, confidence };
}

function ensureObject(value: unknown, field: string): void {
  if (!value || typeof value !== "object") {
    throw new AgentValidationError(`${capitalize(field)} must be an object`, field, value);
  }
}

function ensureNotArray(value: unknown, field: string): void {
  if (Array.isArray(value)) {
    throw new AgentValidationError(`${capitalize(field)} must be a non-array object`, field, value);
  }
}

function normalizeType(type: unknown): "document" | "file" | "external" {
  const t = String(type || "document");
  if (!["document", "file", "external"].includes(t)) {
    throw new AgentValidationError("Invalid source type", "type", t);
  }
  return t as "document" | "file" | "external";
}

function normalizeUrl(url: unknown): string | undefined {
  return url ? String(url) : undefined;
}

function normalizeConfidence(confidence: unknown): number {
  const c = typeof confidence === "number" ? confidence : 1.0;
  if (c < 0 || c > 1) {
    throw new AgentValidationError("Confidence must be between 0 and 1", "confidence", c);
  }
  return c;
}

function capitalize(s: string): string {
  return s.charAt(0).toUpperCase() + s.slice(1);
}

/**
 * Validates and normalizes agent metadata
 */
export function validateAgentMetadata(metadata: unknown): AgentMetadata {
  if (!metadata || typeof metadata !== "object") {
    throw new AgentValidationError("Metadata must be an object", "metadata", metadata);
  }

  const metadataObj = metadata as Record<string, unknown>;

  const tokensUsed = typeof metadataObj.tokensUsed === "number" ? metadataObj.tokensUsed : 0;
  const processingTime =
    typeof metadataObj.processingTime === "number" ? metadataObj.processingTime : 0;
  const model = String(metadataObj.model || "unknown");

  // Validate numeric fields
  if (tokensUsed < 0) {
    throw new AgentValidationError("Tokens used cannot be negative", "tokensUsed", tokensUsed);
  }

  if (processingTime < 0) {
    throw new AgentValidationError(
      "Processing time cannot be negative",
      "processingTime",
      processingTime,
    );
  }

  return {
    tokensUsed,
    processingTime,
    model,
  };
}

/**
 * Assesses search quality based on average score
 */
export function assessSearchQuality(averageScore: number): "high" | "medium" | "low" {
  if (averageScore >= 0.8) return "high";
  if (averageScore >= 0.6) return "medium";
  return "low";
}

// ============================================================================
// SIMPLE LOGGER
// ============================================================================

export interface LogContext {
  requestId?: string;
  traceId?: string;
  spanId?: string;
  operation?: string;
  resource?: string;
  [key: string]: unknown;
}

export class SimpleLogger {
  private static instance: SimpleLogger;
  private isDevelopment = process.env.NODE_ENV === "development";

  static getInstance(): SimpleLogger {
    if (!SimpleLogger.instance) {
      SimpleLogger.instance = new SimpleLogger();
    }
    return SimpleLogger.instance;
  }

  createChildLogger(_context: LogContext): SimpleLogger {
    return this;
  }

  info(message: string, data?: unknown): void {
    if (this.isDevelopment) {
      console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : "");
    }
  }

  error(message: string, data?: unknown): void {
    console.error(`[ERROR] ${message}`, data ? JSON.stringify(data, null, 2) : "");
  }

  warn(message: string, data?: unknown): void {
    console.warn(`[WARN] ${message}`, data ? JSON.stringify(data, null, 2) : "");
  }

  debug(message: string, data?: unknown): void {
    if (this.isDevelopment) {
      console.debug(`[DEBUG] ${message}`, data ? JSON.stringify(data, null, 2) : "");
    }
  }
}

// ============================================================================
// STREAMING RESPONSE HANDLER
// ============================================================================

class StreamingResponseHandler {
  private fullResponse = "";
  private searchResults: AgentSource[] = [];
  private confidenceData: ConfidenceData | null = null;
  private metadata: Record<string, unknown> = {};
  private chunkCount = 0;
  private startTime: number;

  constructor(startTime: number) {
    this.startTime = startTime;
  }

  /**
   * Process streaming chunk
   */
  processChunk(chunk: AgentStreamChunk, onProgress: (progress: StreamingProgress) => void): void {
    this.chunkCount++;

    // Handle content
    if (chunk.content) {
      this.fullResponse += chunk.content;
      onProgress({
        type: "content",
        content: chunk.content,
        fullResponse: this.fullResponse,
        chunkIndex: this.chunkCount,
        elapsedMs: Date.now() - this.startTime,
      });
    }

    // Handle sources
    if (chunk.sources) {
      this.searchResults = chunk.sources;
      onProgress({
        type: "metadata",
        searchResults: chunk.sources,
        chunkIndex: this.chunkCount,
        elapsedMs: Date.now() - this.startTime,
      });
    }

    // Handle metadata
    if (chunk.metadata) {
      this.metadata = { ...this.metadata, ...chunk.metadata };
      // Attempt to extract confidence information from metadata if present
      const metaAny = chunk.metadata as unknown as Record<string, unknown>;
      if (typeof metaAny?.confidence_score === "number") {
        this.confidenceData = {
          score: metaAny.confidence_score,
          reason:
            typeof metaAny.confidence_reason === "string"
              ? metaAny.confidence_reason
              : "Confidence provided by model",
          isHighConfidence: metaAny.confidence_score >= 0.7,
        };
      }
      onProgress({
        type: "metadata",
        metadata: this.metadata,
        chunkIndex: this.chunkCount,
        elapsedMs: Date.now() - this.startTime,
      });
    }
  }

  /**
   * Build final streaming response
   */
  buildFinalResponse(): ProcessedStreamingResponse {
    const executionTime = Date.now() - this.startTime;

    return {
      content: this.fullResponse,
      confidence: this.processConfidence(),
      searchResults: this.processSearchResults(),
      performance: {
        executionTime,
        totalChunks: this.chunkCount,
        averageChunkTime: this.chunkCount > 0 ? executionTime / this.chunkCount : 0,
      },
      metadata: this.metadata,
    };
  }

  /**
   * Process confidence from streaming data
   */
  private processConfidence(): ConfidenceInfo {
    if (!this.confidenceData) {
      return {
        score: 0,
        reason: "No confidence data available",
        isHighConfidence: false,
        isLowConfidence: true,
        needsReview: true,
      };
    }

    return {
      score: this.confidenceData.score,
      reason: this.confidenceData.reason,
      isHighConfidence: this.confidenceData.isHighConfidence,
      isLowConfidence: !this.confidenceData.isHighConfidence,
      needsReview: this.confidenceData.score < 0.3,
    };
  }

  /**
   * Process search results from streaming data
   */
  private processSearchResults(): ProcessedSearchResults {
    const documents = this.searchResults.map((result) => ({
      id: result.id,
      content: result.title, // Use title as content for now
      relevance: result.confidence,
      metadata: {},
    }));

    const averageRelevance =
      this.searchResults.length > 0
        ? this.searchResults.reduce((sum, result) => sum + result.confidence, 0) /
          this.searchResults.length
        : 0;

    return {
      documents,
      averageRelevance,
      quality: assessSearchQuality(averageRelevance),
      count: this.searchResults.length,
    };
  }
}

// ============================================================================
// MAIN AGENT STREAMING SERVICE
// ============================================================================

export class AgentStreamingService {
  private logger = SimpleLogger.getInstance();
  private config: AgentInvokeConfig;

  constructor(config: AgentInvokeConfig) {
    this.config = config;
  }

  /**
   * Invoke agent with streaming response
   */
  async invokeAgentStream(
    request: AgentInvokeRequest,
    correlationContext: RequestContext,
    onChunk: (chunk: AgentStreamChunk) => void,
  ): Promise<void> {
    const startTime = Date.now();
    const requestLogger = this.logger.createChildLogger({
      requestId: correlationContext.requestId,
      traceId: correlationContext.traceId,
      spanId: correlationContext.spanId,
      operation: "agent_invoke_stream",
      resource: "external_agent",
    });

    const messageText =
      typeof request.input === "string"
        ? request.input
        : ((request.input as Record<string, unknown>).message ??
          (request.input as Record<string, unknown>).text ??
          (request.input as Record<string, unknown>).query ??
          "");
    const messageLength = typeof messageText === "string" ? messageText.length : 0;
    requestLogger.info("Starting agent invoke with streaming", {
      organizationId: request.context.organizationId,
      userId: request.context.userId,
      sessionId: request.context.sessionId,
      agentName: request.agent_name,
      messageLength,
      requestId: request.context.requestId,
      platform: request.context.platform,
    });

    try {
      await this.retryRequest(
        () => this.makeStreamingRequest(request, correlationContext, onChunk),
        {
          maxRetries: this.config.retries,
          baseDelay: 1000,
          maxDelay: 10000,
          backoffMultiplier: 2,
        },
      );

      const endTime = Date.now();
      requestLogger.info("Agent streaming invoke completed successfully", {
        duration: endTime - startTime,
      });
    } catch (error) {
      const endTime = Date.now();
      requestLogger.error("Agent streaming invoke failed", {
        duration: endTime - startTime,
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
      });
      throw this.handleError(error);
    }
  }

  /**
   * Enhanced streaming with processed response
   */
  async invokeAgentStreamWithProcessing(
    request: AgentInvokeRequest,
    correlationContext: RequestContext,
    onProgress: (progress: StreamingProgress) => void,
  ): Promise<ProcessedStreamingResponse> {
    const startTime = Date.now();
    const streamingHandler = new StreamingResponseHandler(startTime);

    try {
      await this.invokeAgentStream(request, correlationContext, (chunk) =>
        streamingHandler.processChunk(chunk, onProgress),
      );

      return streamingHandler.buildFinalResponse();
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Make streaming request to agent service
   */
  private async makeStreamingRequest(
    request: AgentInvokeRequest,
    correlationContext: RequestContext,
    onChunk: (chunk: AgentStreamChunk) => void,
  ): Promise<void> {
    const url = new URL("/v1/external/agent/stream", this.config.baseUrl);
    const isHttps = url.protocol === "https:";

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Accept: "text/event-stream",
      "X-Internal-Secret": this.config.internalSecret,
      "X-Request-ID": correlationContext.requestId,
      "X-Trace-ID": correlationContext.traceId,
      "X-Span-ID": correlationContext.spanId,
    };

    // Only add optional headers if they have values
    if (correlationContext.userId) {
      headers["X-User-ID"] = correlationContext.userId;
    }
    if (correlationContext.organizationId) {
      headers["X-Organization-ID"] = correlationContext.organizationId;
    }

    const requestBody = JSON.stringify(request);

    const requestLogger = this.logger.createChildLogger({
      requestId: correlationContext.requestId,
      traceId: correlationContext.traceId,
      spanId: correlationContext.spanId,
      operation: "agent_streaming_request",
      resource: "external_agent",
    });

    // Log the exact request for external API admin inspection
    const redactedHeaders: Record<string, string | undefined> = {
      ...headers,
      "X-Internal-Secret": headers["X-Internal-Secret"] ? "[REDACTED]" : undefined,
    };
    requestLogger.info("EXTERNAL_API_DEBUG: Streaming request details for admin inspection", {
      timestamp: new Date().toISOString(),
      requestId: correlationContext.requestId,
      url: url.toString(),
      method: "POST",
      isHttps,
      headers: redactedHeaders,
      headersDebug: {
        hasInternalSecret: !!headers["X-Internal-Secret"],
        internalSecretLength: headers["X-Internal-Secret"]?.length || 0,
        allHeaderKeys: Object.keys(headers),
        configInternalSecret: this.config.internalSecret ? "[REDACTED]" : "undefined",
        configInternalSecretLength: this.config.internalSecret?.length || 0,
      },
      payload: request,
      payloadSize: requestBody.length,
      config: {
        baseUrl: this.config.baseUrl,
        timeout: this.config.timeout,
        retries: this.config.retries,
        sslVerify: this.config.sslVerify,
      },
      // Raw request details for external API admin
      rawRequest: {
        url: url.toString(),
        method: "POST",
        headers: redactedHeaders,
        body: requestBody,
        userAgent: "Anter-AgentService/1.0",
        contentType: headers["Content-Type"],
        acceptHeader: headers["Accept"],
        internalSecretHeader: headers["X-Internal-Secret"] ? "PRESENT" : "MISSING",
        correlationHeaders: {
          requestId: headers["X-Request-ID"],
          traceId: headers["X-Trace-ID"],
          spanId: headers["X-Span-ID"],
          userId: headers["X-User-ID"],
          organizationId: headers["X-Organization-ID"],
        },
      },
    });

    return new Promise((resolve, reject) => {
      const client = isHttps ? https : http;

      const requestOptions: Record<string, unknown> = {
        method: "POST",
        headers,
        timeout: this.config.timeout,
      };
      if (isHttps) {
        requestOptions.rejectUnauthorized = this.config.sslVerify;
      }
      const req = client.request(url, requestOptions, (res) => {
        if (res.statusCode !== 200) {
          let errorData = "";
          res.on("data", (chunk) => {
            errorData += chunk;
          });
          res.on("end", () => {
            // Log detailed error information for external API admin
            requestLogger.error(
              "EXTERNAL_API_DEBUG: Streaming request failed - details for admin inspection",
              {
                timestamp: new Date().toISOString(),
                requestId: correlationContext.requestId,
                statusCode: res.statusCode,
                statusMessage: res.statusMessage,
                responseHeaders: res.headers,
                responseData: errorData,
                errorDetails: {
                  httpStatus: res.statusCode,
                  statusText: res.statusMessage,
                  responseBody: errorData,
                  responseHeaders: Object.keys(res.headers),
                  contentType: res.headers["content-type"],
                  contentLength: res.headers["content-length"],
                  server: res.headers["server"],
                  date: res.headers["date"],
                },
              },
            );

            reject(new Error(`Agent streaming service returned ${res.statusCode}: ${errorData}`));
          });
          return;
        }

        let buffer = "";
        let eventLines: string[] = [];
        const MAX_STREAM_BUFFER_BYTES = 5 * 1024 * 1024; // 5MB guard
        const MAX_EVENT_BYTES = 1 * 1024 * 1024; // 1MB per event

        res.on("data", (chunk) => {
          buffer += chunk.toString();
          if (buffer.length > MAX_STREAM_BUFFER_BYTES) {
            requestLogger.error("Streaming buffer exceeded limit", {
              limit: MAX_STREAM_BUFFER_BYTES,
            });
            req.destroy();
            reject(new Error("Streaming buffer exceeded limit"));
            return;
          }

          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          const invokeOnChunk = (
            streamChunk: AgentStreamChunk,
            dataObj: Record<string, unknown>,
            formatLabel: "SSE" | "JSON",
          ): boolean => {
            // Skip empty chunks
            const hasMeaningful = Boolean(
              streamChunk.content ||
                streamChunk.sources ||
                streamChunk.metadata ||
                streamChunk.isComplete,
            );
            if (!hasMeaningful) return false;
            try {
              onChunk(streamChunk);
            } catch (e) {
              requestLogger.error("onChunk callback threw", {
                error: e instanceof Error ? e.message : String(e),
              });
              req.destroy();
              reject(new Error("Consumer callback failed"));
              return true;
            }
            if (streamChunk.isComplete) {
              requestLogger.info(
                `EXTERNAL_API_DEBUG: Final streaming response object received (${formatLabel} format)`,
                {
                  timestamp: new Date().toISOString(),
                  requestId: correlationContext.requestId,
                  finalResponse: {
                    type: (dataObj && dataObj.type) || undefined,
                    content: (dataObj && dataObj.content) || undefined,
                    metadata: (dataObj && dataObj.metadata) || undefined,
                    isComplete: (dataObj && dataObj.isComplete) || undefined,
                    sources: (dataObj && dataObj.sources) || undefined,
                  },
                  rawData: dataObj,
                  streamChunk,
                },
              );
              resolve();
              return true;
            }
            return false;
          };

          for (const rawLine of lines) {
            const line = rawLine.replace(/\r$/, "");
            if (line === "") {
              // End of SSE event
              if (eventLines.length > 0) {
                const dataPayload = eventLines
                  .map((l) => (l.startsWith("data:") ? l.slice(5).replace(/^\s/, "") : l))
                  .join("\n");
                eventLines = [];
                if (dataPayload.length > 0) {
                  if (dataPayload.length > MAX_EVENT_BYTES) {
                    requestLogger.warn("Skipping oversized SSE event", {
                      size: dataPayload.length,
                      limit: MAX_EVENT_BYTES,
                    });
                    continue;
                  }
                  try {
                    const rawData = safeJsonParse(dataPayload);
                    const data = this.validateStreamingChunk(rawData);
                    const streamChunk: AgentStreamChunk = {
                      content: data.content || "",
                      isComplete: data.isComplete || data.type === "complete" || false,
                      sources: data.sources,
                      metadata: data.metadata,
                    };
                    if (invokeOnChunk(streamChunk, data, "SSE")) return;
                  } catch (e) {
                    requestLogger.warn("Failed to parse SSE event payload", {
                      error: e instanceof Error ? e.message : String(e),
                    });
                  }
                }
              }
              continue;
            }

            if (line.startsWith("data:")) {
              eventLines.push(line);
              continue;
            }

            if (line.trim()) {
              // Fallback: try to parse as direct JSON line
              try {
                const rawData = safeJsonParse(line);
                const data = this.validateStreamingChunk(rawData);
                const streamChunk: AgentStreamChunk = {
                  content: data.content || "",
                  isComplete: data.isComplete || data.type === "complete" || false,
                  sources: data.sources,
                  metadata: data.metadata,
                };
                if (invokeOnChunk(streamChunk, data, "JSON")) return;
              } catch {
                // Ignore non-JSON lines
              }
            }
          }
        });

        res.on("end", () => {
          requestLogger.info("EXTERNAL_API_DEBUG: Streaming response completed successfully", {
            timestamp: new Date().toISOString(),
            requestId: correlationContext.requestId,
            responseStatus: "completed",
            responseHeaders: res.headers,
          });
          resolve();
        });
      });

      req.on("error", (error) => {
        requestLogger.error("Agent streaming request failed", {
          error: error.message,
          errorCode: (error as { code?: string }).code,
          errorStack: error.stack,
        });
        reject(new Error(`Streaming request failed: ${error.message}`));
      });

      req.on("timeout", () => {
        requestLogger.error("Agent streaming request timeout", {
          timeout: this.config.timeout,
        });
        req.destroy();
        reject(new Error("Streaming request timeout"));
      });

      req.write(requestBody);
      req.end();
    });
  }

  private async retryRequest<T>(requestFn: () => Promise<T>, config: RetryConfig): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === config.maxRetries) {
          break;
        }

        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt),
          config.maxDelay,
        );

        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Enhanced error handling
   */
  private handleError(error: unknown): never {
    if (error instanceof AgentServiceError) {
      // Re-throw our own error types
      throw error;
    }

    if (error instanceof AgentValidationError) {
      throw error;
    }

    // Handle HTTP errors
    if (error instanceof Error) {
      const message = error.message.toLowerCase();

      if (message.includes("401") || message.includes("unauthorized")) {
        throw new AgentAuthenticationError("Invalid authentication credentials");
      }

      if (message.includes("403") || message.includes("forbidden")) {
        throw new AgentAuthenticationError("Access denied");
      }

      if (message.includes("404") || message.includes("not found")) {
        throw new AgentNotFoundError("Agent or resource not found");
      }

      if (message.includes("429") || message.includes("rate limit")) {
        throw new AgentRateLimitError("Rate limit exceeded, please try again later");
      }

      if (message.includes("timeout")) {
        throw new AgentTimeoutError("Request timed out");
      }

      if (message.includes("network") || message.includes("connection")) {
        throw new AgentNetworkError("Network error occurred", error);
      }
    }

    // Default error handling
    throw new AgentServiceError(
      error instanceof Error ? error.message : String(error),
      undefined,
      undefined,
      error instanceof Error ? error : undefined,
    );
  }

  /**
   * Validate streaming chunk data
   */
  private validateStreamingChunk(data: unknown): {
    content?: string;
    isComplete?: boolean;
    type?: string;
    sources?: AgentSource[];
    metadata?: AgentMetadata;
  } {
    if (!data || typeof data !== "object") {
      throw new AgentValidationError("Streaming chunk must be an object", "data", data);
    }

    const chunk = data as Record<string, unknown>;

    return {
      content: chunk.content ? String(chunk.content) : undefined,
      isComplete: Boolean(chunk.isComplete),
      type: chunk.type ? String(chunk.type) : undefined,
      sources:
        chunk.sources && Array.isArray(chunk.sources)
          ? (chunk.sources
              .map((source) => {
                try {
                  return validateAgentSource(source);
                } catch {
                  return null;
                }
              })
              .filter(Boolean) as AgentSource[])
          : undefined,
      metadata: chunk.metadata ? validateAgentMetadata(chunk.metadata) : undefined,
    };
  }
}

// ============================================================================
// USAGE EXAMPLE
// ============================================================================

/*
// Example usage:
const config: AgentInvokeConfig = {
  baseUrl: 'https://your-agent-service.com',
  internalSecret: 'your-secret-key',
  timeout: 30000,
  retries: 3,
  sslVerify: true
};

const service = new AgentStreamingService(config);

const request: AgentInvokeRequest = {
  agent_name: 'your-agent',
  input: 'Hello, how can you help me?',
  context: {
    sessionId: 'session-123',
    organizationId: 'org-456',
    userId: 'user-789',
    metadata: {
      source: 'web',
      timestamp: new Date().toISOString(),
      query_type: 'general'
    },
    requestId: 'req-123',
    timestamp: new Date().toISOString(),
    platform: 'web'
  }
};

const correlationContext: RequestContext = {
  requestId: 'req-123',
  traceId: 'trace-456',
  spanId: 'span-789',
  sessionId: 'session-123',
  timestamp: Date.now()
};

// Basic streaming
await service.invokeAgentStream(request, correlationContext, (chunk) => {
  console.log('Received chunk:', chunk);
  if (chunk.content) {
    process.stdout.write(chunk.content);
  }
  if (chunk.isComplete) {
    console.log('\nStream completed');
  }
});

// Enhanced streaming with processing
const result = await service.invokeAgentStreamWithProcessing(
  request, 
  correlationContext, 
  (progress) => {
    if (progress.type === 'content') {
      process.stdout.write(progress.content || '');
    }
  }
);

console.log('Final result:', result);
*/
