"use client";

import { useRBACSecurityTest } from "@/hooks/use-rbac-security-test";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw, Shield, User, Settings } from "lucide-react";

export default function RBACTestPage() {
  const { testResults, currentPermissions, runTests, isDevelopment } = useRBACSecurityTest();

  if (!isDevelopment) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              RBAC Security Test
            </CardTitle>
            <CardDescription>This page is only available in development mode.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              The RBAC Security Test component and this test page are only visible when NODE_ENV is
              set to "development".
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">RBAC Security Test</h1>
          <p className="text-gray-600 mt-2">
            Development-only page to test and demonstrate RBAC security functionality
          </p>
        </div>
        <Button onClick={() => runTests()} className="flex items-center gap-2">
          <RefreshCw className="w-4 h-4" />
          Refresh Tests
        </Button>
      </div>

      {/* Current Permissions Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Current User Permissions
          </CardTitle>
          <CardDescription>Real-time RBAC context information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Role</label>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{currentPermissions.role}</Badge>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Permissions Count</label>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{currentPermissions.permissionsCount}</Badge>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Department</label>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{currentPermissions.department || "None"}</Badge>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Access Level</label>
              <div className="flex items-center gap-2">
                {currentPermissions.isOwner && (
                  <Badge className="bg-green-100 text-green-800">Owner</Badge>
                )}
                {currentPermissions.isAdmin && !currentPermissions.isOwner && (
                  <Badge className="bg-blue-100 text-blue-800">Admin</Badge>
                )}
                {!currentPermissions.isOwner && !currentPermissions.isAdmin && (
                  <Badge variant="secondary">User</Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Security Test Results
          </CardTitle>
          <CardDescription>Automated RBAC system health checks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testResults.map((test) => (
              <div key={test.name} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{test.name}</h4>
                  <Badge
                    variant={
                      test.status === "PASS"
                        ? "default"
                        : test.status === "WARNING"
                          ? "secondary"
                          : "destructive"
                    }
                    className={
                      test.status === "PASS"
                        ? "bg-green-100 text-green-800"
                        : test.status === "WARNING"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                    }
                  >
                    {test.status}
                  </Badge>
                </div>
                <p className="text-gray-600 text-sm mb-2">{test.description}</p>
                {test.details && (
                  <div className="bg-gray-50 rounded p-2">
                    <p className="text-xs text-gray-700">{test.details}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Development Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Development Information
          </CardTitle>
          <CardDescription>Information about this development feature</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">About RBAC Security Test</h4>
              <p className="text-sm text-gray-600 mb-2">
                This component provides real-time visibility into the RBAC (Role-Based Access
                Control) system during development. It helps developers understand:
              </p>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Current user permissions and role</li>
                <li>• RBAC system health and status</li>
                <li>• Authentication and authorization state</li>
                <li>• Potential security issues or misconfigurations</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Usage</h4>
              <p className="text-sm text-gray-600">
                The RBAC Security Test component automatically appears on all organization and
                dashboard pages when running in development mode. You can also use the{" "}
                <code className="bg-gray-100 px-1 rounded">useRBACSecurityTest</code> hook to access
                RBAC test functionality in your own components.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
