import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { Shell } from "./shell";
import { BasicDataTable } from "./_components/basic-data-table";
import React from "react";
import { FeatureFlagsProvider } from "@/providers/feature-flags-provider";

export default function DataTablePlaygroundPage() {
  return (
    <Shell className="gap-2">
      <FeatureFlagsProvider>
        <React.Suspense
          fallback={
            <DataTableSkeleton
              columnCount={7}
              filterCount={2}
              cellWidths={["10rem", "30rem", "10rem", "10rem", "6rem", "6rem", "6rem"]}
              shrinkZero
            />
          }
        >
          <BasicDataTable />
        </React.Suspense>
      </FeatureFlagsProvider>
    </Shell>
  );
}
