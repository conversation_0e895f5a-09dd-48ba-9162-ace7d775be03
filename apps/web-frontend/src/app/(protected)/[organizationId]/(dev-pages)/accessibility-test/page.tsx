import { AccessibilityAudit } from "@/components/accessibility-audit";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ThemeToggle } from "@/components/theme-toggle";
import { AskAIButton } from "@/components/ask-ai-button";

export default function AccessibilityTestPage() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Accessibility Testing Dashboard</h1>
          <p className="text-xl text-muted-foreground">
            WCAG 2.2 AA Compliance Testing for AskInfosec Frontend Components
          </p>
          <div className="flex items-center justify-center gap-4">
            <ThemeToggle />
            <AskAIButton />
          </div>
        </div>

        {/* Accessibility Audit Component */}
        <AccessibilityAudit />

        {/* Test Components Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Button Testing */}
          <Card>
            <CardHeader>
              <CardTitle>Button Components</CardTitle>
              <CardDescription>
                Testing button accessibility with various states and variants
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Standard Buttons</h3>
                <div className="flex flex-wrap gap-2">
                  <Button>Primary Button</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="destructive">Destructive</Button>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-semibold">Button Sizes</h3>
                <div className="flex flex-wrap items-center gap-2">
                  <Button size="sm">Small</Button>
                  <Button size="default">Default</Button>
                  <Button size="lg">Large</Button>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-semibold">Button States</h3>
                <div className="flex flex-wrap gap-2">
                  <Button disabled>Disabled Button</Button>
                  <Button aria-pressed="true">Toggle Active</Button>
                  <Button aria-expanded="false">Expandable</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form Testing */}
          <Card>
            <CardHeader>
              <CardTitle>Form Components</CardTitle>
              <CardDescription>
                Testing form accessibility with proper labeling and validation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  aria-describedby="email-help"
                />
                <p id="email-help" className="text-xs text-muted-foreground">
                  We'll never share your email with anyone else.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  aria-describedby="password-help"
                />
                <p id="password-help" className="text-xs text-muted-foreground">
                  Must be at least 8 characters long.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <textarea
                  id="message"
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Type your message here..."
                  aria-describedby="message-help"
                />
                <p id="message-help" className="text-xs text-muted-foreground">
                  Maximum 500 characters.
                </p>
              </div>

              <Button type="submit" className="w-full">
                Submit Form
              </Button>
            </CardContent>
          </Card>

          {/* Navigation Testing */}
          <Card>
            <CardHeader>
              <CardTitle>Navigation Components</CardTitle>
              <CardDescription>
                Testing navigation accessibility and keyboard support
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <nav aria-label="Test navigation">
                <ul className="space-y-2">
                  <li>
                    <a
                      href="#home"
                      className="block p-2 rounded-md hover:bg-accent focus:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1"
                      aria-current="page"
                    >
                      Home
                    </a>
                  </li>
                  <li>
                    <a
                      href="#about"
                      className="block p-2 rounded-md hover:bg-accent focus:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1"
                    >
                      About
                    </a>
                  </li>
                  <li>
                    <a
                      href="#services"
                      className="block p-2 rounded-md hover:bg-accent focus:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1"
                    >
                      Services
                    </a>
                  </li>
                  <li>
                    <a
                      href="#contact"
                      className="block p-2 rounded-md hover:bg-accent focus:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1"
                    >
                      Contact
                    </a>
                  </li>
                </ul>
              </nav>

              <div className="space-y-2">
                <h3 className="font-semibold">Breadcrumb Navigation</h3>
                <nav aria-label="Breadcrumb">
                  <ol className="flex items-center space-x-2 text-sm">
                    <li>
                      <a href="#" className="text-muted-foreground hover:text-foreground">
                        Home
                      </a>
                    </li>
                    <li className="text-muted-foreground">/</li>
                    <li>
                      <a href="#" className="text-muted-foreground hover:text-foreground">
                        Dashboard
                      </a>
                    </li>
                    <li className="text-muted-foreground">/</li>
                    <li aria-current="page" className="font-medium">
                      Accessibility Test
                    </li>
                  </ol>
                </nav>
              </div>
            </CardContent>
          </Card>

          {/* Status and Feedback */}
          <Card>
            <CardHeader>
              <CardTitle>Status & Feedback</CardTitle>
              <CardDescription>
                Testing status indicators and user feedback components
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Status Badges</h3>
                <div className="flex flex-wrap gap-2">
                  <Badge>Default</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="destructive">Error</Badge>
                  <Badge variant="outline">Outline</Badge>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-semibold">Alert Messages</h3>
                <div className="space-y-2">
                  <div
                    className="p-3 rounded-md bg-green-50 border border-green-200 text-green-800"
                    role="alert"
                    aria-live="polite"
                  >
                    <strong>Success:</strong> Your changes have been saved successfully.
                  </div>
                  <div
                    className="p-3 rounded-md bg-yellow-50 border border-yellow-200 text-yellow-800"
                    role="alert"
                    aria-live="polite"
                  >
                    <strong>Warning:</strong> Please review your input before proceeding.
                  </div>
                  <div
                    className="p-3 rounded-md bg-red-50 border border-red-200 text-red-800"
                    role="alert"
                    aria-live="assertive"
                  >
                    <strong>Error:</strong> There was a problem processing your request.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Keyboard Navigation Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Keyboard Navigation Testing</CardTitle>
            <CardDescription>Instructions for testing keyboard accessibility</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">Basic Navigation</h3>
                <ul className="space-y-1 text-sm">
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">Tab</kbd> - Move to next
                    focusable element
                  </li>
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Tab</kbd> - Move to
                    previous focusable element
                  </li>
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">Enter</kbd> - Activate
                    buttons and links
                  </li>
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">Space</kbd> - Activate
                    buttons
                  </li>
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">Escape</kbd> - Close dialogs
                    and menus
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Advanced Navigation</h3>
                <ul className="space-y-1 text-sm">
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">Arrow Keys</kbd> - Navigate
                    within menus and lists
                  </li>
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">Home</kbd> - Move to first
                    item
                  </li>
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">End</kbd> - Move to last
                    item
                  </li>
                  <li>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl + K</kbd> - Open
                    command menu
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
