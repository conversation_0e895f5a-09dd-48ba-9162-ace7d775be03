"use client";

import React, { createContext, useCallback, useContext, useMemo, useState } from "react";
import { ContractsV1Files } from "@askinfosec/types";
import {
  createDocument as createDocumentAction,
  CreateDocumentParams,
  CreateDocumentResponse,
} from "../_actions/create-document";
import { updateDocumentAction, UpdateDocumentParams } from "../_actions/update-document";
import {
  deleteDocuments as deleteDocumentsAction,
  DeleteDocumentsResponse,
} from "../_actions/delete-documents";
import { downloadDocument as downloadDocumentAction } from "../_actions/download-document";
import {
  extractDocumentForEmbeddings,
  GenerateEmbeddingsResponse,
} from "../_actions/generate-embeddings";

// File upload state interfaces
interface UploadFile {
  file: File;
  id: string;
  status: "pending" | "uploading" | "success" | "error";
  progress?: number;
  error?: string;
}

interface DocumentsContextValue {
  organizationId: string;
  // Central refresh signal for readers (e.g., table) to react and refetch
  refreshVersion: number;
  requestRefresh: () => void;

  // Local add/open signals for table to react without ref coupling
  lastAdded?: ContractsV1Files.FileDTO;
  lastAddedVersion: number;
  addDocumentLocal: (doc: ContractsV1Files.FileDTO) => void;

  openDocumentId?: string;
  openVersion: number;
  openDocumentLocal: (id: string) => void;

  // File upload state
  selectedFiles: UploadFile[];
  setSelectedFiles: (files: UploadFile[]) => void;
  addSelectedFiles: (files: File[]) => void;
  removeSelectedFile: (fileId: string) => void;
  clearSelectedFiles: () => void;

  // CRUD surface
  create: (params: CreateDocumentParams) => Promise<CreateDocumentResponse>;
  update: (params: UpdateDocumentParams) => Promise<ContractsV1Files.FileUpdateResponse>;
  remove: (fileIds: string[]) => Promise<DeleteDocumentsResponse>;
  download: (fileId: string) => ReturnType<typeof downloadDocumentAction>;
  generateEmbeddings: (fileId: string) => Promise<GenerateEmbeddingsResponse>;
}

const DocumentsContext = createContext<DocumentsContextValue | undefined>(undefined);

interface DocumentsProviderProps {
  organizationId: string;
  children: React.ReactNode;
}

function generateTempId(nameHint: string) {
  if (typeof crypto !== "undefined" && typeof crypto.randomUUID === "function") {
    return crypto.randomUUID();
  }
  // Safe fallback when crypto.randomUUID is unavailable
  return `${nameHint}-${Date.now()}-${Math.random().toString(36).slice(2, 10)}`;
}

export function DocumentsProvider({ organizationId, children }: DocumentsProviderProps) {
  const [refreshVersion, setRefreshVersion] = useState(0);
  const [lastAdded, setLastAdded] = useState<ContractsV1Files.FileDTO | undefined>(undefined);
  const [lastAddedVersion, setLastAddedVersion] = useState(0);
  const [openDocumentId, setOpenDocumentId] = useState<string | undefined>(undefined);
  const [openVersion, setOpenVersion] = useState(0);
  const [selectedFiles, setSelectedFiles] = useState<UploadFile[]>([]);

  const requestRefresh = useCallback(() => {
    setRefreshVersion((v) => v + 1);
  }, []);

  const addDocumentLocal = useCallback((doc: ContractsV1Files.FileDTO) => {
    setLastAdded(doc);
    setLastAddedVersion((v) => v + 1);
  }, []);

  const openDocumentLocal = useCallback((id: string) => {
    setOpenDocumentId(id);
    setOpenVersion((v) => v + 1);
  }, []);

  // File upload state management
  const addSelectedFiles = useCallback((files: File[]) => {
    const newUploadFiles: UploadFile[] = files.map((file) => ({
      file,
      id: generateTempId(file.name),
      status: "pending" as const,
    }));
    setSelectedFiles((prev) => [...prev, ...newUploadFiles]);
  }, []);

  const removeSelectedFile = useCallback((fileId: string) => {
    setSelectedFiles((prev) => prev.filter((f) => f.id !== fileId));
  }, []);

  const clearSelectedFiles = useCallback(() => {
    setSelectedFiles([]);
  }, []);

  const create = useCallback<DocumentsContextValue["create"]>(
    async (params) => {
      const result = await createDocumentAction(params);
      if (result.success && result.document) {
        addDocumentLocal(result.document);
        openDocumentLocal(result.document.id);
        requestRefresh();
      }
      return result;
    },
    [addDocumentLocal, openDocumentLocal, requestRefresh],
  );

  const update = useCallback<DocumentsContextValue["update"]>(async (params) => {
    const result = await updateDocumentAction(params);
    return result;
  }, []);

  const remove = useCallback<DocumentsContextValue["remove"]>(
    async (fileIds) => {
      const result = await deleteDocumentsAction({ organizationId, fileIds });
      if (result.success) {
        requestRefresh();
      }
      return result;
    },
    [organizationId, requestRefresh],
  );

  const download = useCallback<DocumentsContextValue["download"]>(
    async (fileId) => {
      return downloadDocumentAction(organizationId, fileId);
    },
    [organizationId],
  );

  const generateEmbeddings = useCallback<DocumentsContextValue["generateEmbeddings"]>(
    async (fileId) => {
      try {
        // First, extract document content using server action
        const extractResult = await extractDocumentForEmbeddings({ organizationId, fileId });

        if (!extractResult.success || !extractResult.documentData) {
          return {
            success: false,
            error: extractResult.error || "Failed to extract document content",
          };
        }

        // Then call the embeddings API from client-side with absolute URL
        const baseUrl = window.location.origin;
        const response = await fetch(`${baseUrl}/api/ai/embeddings`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            documents: [
              {
                id: fileId,
                name: extractResult.documentData.name,
                content: extractResult.documentData.content,
              },
            ],
            options: {
              maxTokens: 2000,
              overlapTokens: 200,
              clearExisting: true,
            },
            organizationId,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          return {
            success: false,
            error: errorData.error || `API request failed: ${response.status}`,
          };
        }

        const result = await response.json();

        if (result && result.success) {
          return {
            success: true,
            message:
              result.message ||
              `Successfully generated embeddings for "${extractResult.documentData.name}". The document is now searchable and can be used by AI assistants.`,
          };
        } else {
          return {
            success: false,
            error: result?.error || "Failed to generate embeddings. Please try again.",
          };
        }
      } catch (error) {
        console.error("Error generating embeddings:", error);
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "An unexpected error occurred while generating embeddings.",
        };
      }
    },
    [organizationId],
  );

  const value = useMemo<DocumentsContextValue>(
    () => ({
      organizationId,
      refreshVersion,
      requestRefresh,
      lastAdded,
      lastAddedVersion,
      addDocumentLocal,
      openDocumentId,
      openVersion,
      openDocumentLocal,
      selectedFiles,
      setSelectedFiles,
      addSelectedFiles,
      removeSelectedFile,
      clearSelectedFiles,
      create,
      update,
      remove,
      download,
      generateEmbeddings,
    }),
    [
      organizationId,
      refreshVersion,
      requestRefresh,
      lastAdded,
      lastAddedVersion,
      addDocumentLocal,
      openDocumentId,
      openVersion,
      openDocumentLocal,
      selectedFiles,
      addSelectedFiles,
      removeSelectedFile,
      clearSelectedFiles,
      create,
      update,
      remove,
      download,
      generateEmbeddings,
    ],
  );

  return <DocumentsContext.Provider value={value}>{children}</DocumentsContext.Provider>;
}

export function useDocumentsContext(): DocumentsContextValue {
  const ctx = useContext(DocumentsContext);
  if (!ctx) {
    throw new Error("useDocumentsContext must be used within a DocumentsProvider");
  }
  return ctx;
}
