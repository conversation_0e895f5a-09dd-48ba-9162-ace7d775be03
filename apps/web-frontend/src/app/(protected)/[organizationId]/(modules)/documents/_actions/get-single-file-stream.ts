"use server";

import { apiGet } from "@/lib/backend-api";

export async function getSingleFileStream(organizationId: string, fileId: string) {
  try {
    const res = await apiGet(`/v1/organizations/${organizationId}/files/${fileId}`);
    if (!res.ok) {
      return { success: false, error: `Request failed: ${res.status}` };
    }

    // Read as ArrayBuffer
    const arrayBuffer = await res.arrayBuffer();

    return { success: true, data: arrayBuffer };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
}
