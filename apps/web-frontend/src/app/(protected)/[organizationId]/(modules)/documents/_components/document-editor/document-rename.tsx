"use client";

import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Check, X, Edit3, FileText } from "lucide-react";
import { cn } from "@/lib/utils";

interface DocumentRenameProps {
  documentName: string;
  onRename?: (newName: string) => void;
  className?: string;
}

export function DocumentRename({ documentName, onRename, className }: DocumentRenameProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(documentName);
  const [isHovered, setIsHovered] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update edit value when document name changes
  useEffect(() => {
    setEditValue(documentName);
  }, [documentName]);

  // Focus input when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleStartEdit = () => {
    setIsEditing(true);
    setEditValue(documentName);
  };

  const handleSave = () => {
    const trimmedValue = editValue.trim();
    if (trimmedValue && trimmedValue !== documentName) {
      onRename?.(trimmedValue);
    }
    // Keep the rename control visible as read-only label after save; do not close sheet
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(documentName);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSave();
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="flex items-center gap-2 flex-1">
          <FileText className="h-5 w-5 text-muted-foreground flex-shrink-0" />
          <Input
            ref={inputRef}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            className="text-lg font-semibold h-8 px-2 flex-1"
            placeholder="Document name"
          />
        </div>
        <Button
          size="sm"
          variant="ghost"
          onClick={handleSave}
          className="h-8 w-8 p-0 hover:bg-green-100 hover:text-green-600"
        >
          <Check className="h-4 w-4" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={handleCancel}
          className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "flex items-center gap-2 text-lg font-semibold cursor-pointer group",
        "hover:bg-accent/50 rounded-md px-2 py-1 transition-colors",
        className,
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleStartEdit}
    >
      <FileText className="h-5 w-5 text-muted-foreground flex-shrink-0" />
      <span className="flex-1 truncate">{documentName}</span>
      {isHovered && (
        <Edit3 className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
      )}
    </div>
  );
}
