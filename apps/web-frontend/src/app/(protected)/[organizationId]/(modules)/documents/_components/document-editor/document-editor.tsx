"use client";

import { ContractsV1Files } from "@askinfosec/types";
import { SimpleEditor } from "@/components/editor/tiptap/tiptap-templates/simple/simple-editor";

interface DocumentEditorProps {
  document: ContractsV1Files.FileDTO;
  organizationId: string;
  onSave?: (updatedDocument: Partial<ContractsV1Files.FileDTO>) => void;
  onCancel?: () => void;
  triggerSave?: boolean;
  // Optional session id to reset inner editor state when container reopens
  // (forwarded to SimpleEditor)
  sessionId?: number;
}

export const DocumentEditor = ({
  document,
  organizationId: _organizationId,
  onSave: onSave,
  onCancel: onCancel,
  triggerSave,
  sessionId,
}: DocumentEditorProps) => {
  const handleSave = (content: string) => {
    onSave?.({ content });
  };

  return (
    <SimpleEditor
      content={document.content || ""}
      onSave={handleSave}
      onCancel={onCancel}
      triggerSave={triggerSave}
      canEdit={true}
      sessionId={sessionId}
      onDirtyChange={(dirty) => {
        if (typeof window !== "undefined") {
          const event = new window.CustomEvent("document-editor:dirty", {
            detail: { dirty },
          });
          window.dispatchEvent(event);
        }
      }}
    />
  );
};
