"use client";

import React, { useState, useMemo } from "react";

interface PdfViewerIframeProps {
  arrayBuffer: ArrayBuffer;
}

const PdfViewerIframe = ({ arrayBuffer }: PdfViewerIframeProps) => {
  const [scale, _setScale] = useState(1);

  // Create blob URL for the PDF
  const pdfUrl = useMemo(() => {
    const blob = new Blob([arrayBuffer], { type: "application/pdf" });
    return URL.createObjectURL(blob);
  }, [arrayBuffer]);

  return (
    <div className="max-w-full h-auto overflow-hidden bg-background">
      <iframe
        src={`${pdfUrl}#zoom=${Math.round(scale * 100)}&view=FitH&toolbar=1&navpanes=0&scrollbar=1`}
        className="w-full h-full border-none"
        title="PDF Viewer"
        style={{
          height: "calc(100vh - 72px)",
        }}
      />
    </div>
  );
};

export default PdfViewerIframe;
