"use server";

import { api<PERSON>et<PERSON><PERSON> } from "@/lib/backend-api";
import { ContractsV1Files } from "@askinfosec/types";
import { downloadDocument } from "./download-document";

export interface GenerateEmbeddingsParams {
  organizationId: string;
  fileId: string;
}

export interface GenerateEmbeddingsResponse {
  success: boolean;
  message?: string;
  error?: string;
  documentData?: {
    content: string;
    name: string;
  };
}

/**
 * Extract text content from a document file
 */
async function extractDocumentContent(
  organizationId: string,
  fileId: string,
): Promise<{ content: string; name: string } | null> {
  try {
    // Get the document from the files list (since there's no individual file endpoint)
    const filesResponse = await apiGet<PERSON>son<ContractsV1Files.GetFilesResponse>(
      `/v1/organizations/${organizationId}/files?limit=1000`,
    );

    const document = filesResponse.files.find((file) => file.id === fileId);

    if (!document) {
      throw new Error("Document not found");
    }

    let content = "";

    // Priority 1: Check if the document has direct content field (for system-created documents)
    if (document.content) {
      // Remove HTML tags and convert to plain text
      content = document.content
        .replace(/<br\s*\/?>/gi, "\n")
        .replace(/<\/p>/gi, "\n\n")
        .replace(/<[^>]*>/g, "")
        .replace(/&nbsp;/g, " ")
        .replace(/&amp;/g, "&")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&quot;/g, '"')
        .trim();
    }

    // Priority 2: Check if the document has bufferFile content (base64 encoded binary)
    if (!content && document.bufferFile) {
      try {
        const buffer = globalThis.Buffer.from(document.bufferFile, "base64");
        // For text-based files, try to decode as UTF-8
        // For binary files (PDF, DOCX, etc.), this might not work well
        // In a production system, you'd want proper text extraction for different file types
        const textContent = buffer.toString("utf-8");

        // Basic check to see if it's readable text (not binary)
        if (
          textContent &&
          !textContent.substring(0, 100).includes("\0") &&
          !textContent.substring(0, 100).includes("\b") &&
          !textContent.substring(0, 100).includes("\u000E") &&
          !textContent.substring(0, 100).includes("\u001F") &&
          !textContent.substring(0, 100).includes("\u007F")
        ) {
          content = textContent.trim();
        }
      } catch (error) {
        console.warn("Failed to extract content from bufferFile:", error);
      }
    }

    // Priority 3: If still no content, try to use the download action
    if (!content) {
      try {
        // Use the download action to get the document content
        const downloadResult = await downloadDocument(organizationId, fileId);

        if (downloadResult.success && downloadResult.data?.buffer) {
          const buffer = globalThis.Buffer.from(downloadResult.data.buffer, "base64");
          const textContent = buffer.toString("utf-8");

          // Basic check for readable text
          if (
            textContent &&
            !textContent.substring(0, 100).includes("\0") &&
            !textContent.substring(0, 100).includes("\b") &&
            !textContent.substring(0, 100).includes("\u000E") &&
            !textContent.substring(0, 100).includes("\u001F") &&
            !textContent.substring(0, 100).includes("\u007F")
          ) {
            content = textContent.trim();
          }
        }
      } catch (error) {
        console.warn("Failed to download and extract document content:", error);
      }
    }

    if (!content || content.trim().length === 0) {
      throw new Error(
        "No extractable text content found in document. The document may be binary or empty.",
      );
    }

    // Validate content length (embeddings APIs usually have limits)
    if (content.length > 1000000) {
      // 1MB limit
      console.warn("Document content is very large, truncating for embeddings");
      content = content.substring(0, 1000000) + "... [truncated]";
    }

    return {
      content: content.trim(),
      name: document.name || "Untitled Document",
    };
  } catch (error) {
    console.error("Error extracting document content:", error);
    return null;
  }
}

/**
 * Extract document content for embeddings generation
 */
export async function extractDocumentForEmbeddings({
  organizationId,
  fileId,
}: GenerateEmbeddingsParams): Promise<GenerateEmbeddingsResponse> {
  try {
    // Extract document content
    const documentData = await extractDocumentContent(organizationId, fileId);

    if (!documentData) {
      return {
        success: false,
        error:
          "Failed to extract document content. Please ensure the document contains readable text.",
      };
    }

    // Return the document data for client-side processing
    return {
      success: true,
      message: `Document content extracted successfully. Ready for embeddings generation.`,
      documentData,
    };
  } catch (error) {
    console.error("Error extracting document content:", error);

    let errorMessage = "An unexpected error occurred while extracting document content.";

    if (error instanceof Error) {
      if (error.message.includes("not found")) {
        errorMessage = "Document not found. Please ensure the document exists.";
      } else if (error.message.includes("content")) {
        errorMessage = "Unable to extract readable content from the document.";
      } else if (error.message.includes("network") || error.message.includes("fetch")) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}
