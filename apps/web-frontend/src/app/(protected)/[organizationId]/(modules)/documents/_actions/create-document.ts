"use server";

import { api<PERSON><PERSON><PERSON><PERSON>, ApiError } from "@/lib/backend-api";
import { ContractsV1Files } from "@askinfosec/types";

export interface CreateDocumentParams {
  organizationId: string;
  name: string;
  documentType?: ContractsV1Files.DocumentType;
  content?: string;
}

export interface CreateDocumentResponse {
  success: boolean;
  message?: string;
  document?: ContractsV1Files.FileDTO;
}

export async function createDocument(
  params: CreateDocumentParams,
): Promise<CreateDocumentResponse> {
  const { organizationId, name, documentType, content } = params;

  // Prepare the request body according to the backend API
  const requestBody: Partial<ContractsV1Files.FileDTO> = {
    name,
    documentType: documentType || undefined,
    content: content || "",
  };

  const endpoint = `/v1/organizations/${organizationId}/files`;

  try {
    const response = await apiPostJson<ContractsV1Files.FileDTO>(endpoint, requestBody);

    return {
      success: true,
      document: response,
      message: "Document created successfully",
    };
  } catch (error) {
    console.error("Error creating document:", error);

    if (error instanceof ApiError) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: "Unexpected error while creating document",
    };
  }
}
