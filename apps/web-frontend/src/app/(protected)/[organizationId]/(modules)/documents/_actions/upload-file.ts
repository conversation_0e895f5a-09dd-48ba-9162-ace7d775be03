"use server";

import { api<PERSON><PERSON><PERSON><PERSON>, ApiError } from "@/lib/backend-api";
import { ContractsV1Files } from "@askinfosec/types";

export interface UploadBase64Params {
  organizationId: string;
  name: string;
  bufferBase64: string; // no data: prefix
  checksum?: string; // sha256 hex
  content?: string; // optional extracted text/HTML content
}

export interface UploadBase64Response {
  success: boolean;
  message?: string;
  file?: ContractsV1Files.FileDTO;
}

export async function uploadBase64(params: UploadBase64Params): Promise<UploadBase64Response> {
  const { organizationId, name, bufferBase64, checksum, content } = params;
  const endpoint = `/v1/organizations/${organizationId}/files/uploads`;

  try {
    console.log("Upload attempt:", {
      organizationId,
      name,
      bufferBase64Length: bufferBase64.length,
      checksum,
      contentLength: content?.length || 0,
    });

    const response = await api<PERSON>ost<PERSON><PERSON><ContractsV1Files.FileDTO>(endpoint, {
      name,
      bufferBase64,
      checksum,
      content,
    });

    return { success: true, file: response };
  } catch (error) {
    console.error("Upload API error:", {
      error,
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStatus: error instanceof ApiError ? error.status : undefined,
      errorDetails: error instanceof ApiError ? error.details : undefined,
    });

    if (error instanceof ApiError) {
      return { success: false, message: error.message };
    }
    return { success: false, message: "Unexpected error while uploading file" };
  }
}
