"use client";

import { useState } from "react";
import { ContractsV1Files } from "@askinfosec/types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarDays, FileText, Tag, Clock, Pencil, Check, X, DatabaseIcon } from "lucide-react";

// Helper function to create options from enum schema
const createOptionsFromEnum = (enumObject: Record<string, string>) => {
  return Object.values(enumObject).map((value) => ({
    label: value.charAt(0).toUpperCase() + value.slice(1),
    value: value,
  }));
};

interface DocumentDetailsPanelProps {
  document: ContractsV1Files.FileDTO;
  className?: string;
  onLocalUpdate?: (updated: Partial<ContractsV1Files.FileDTO> & { id: string }) => void;
}

export const DocumentDetailsPanel = ({
  document,
  className,
  onLocalUpdate,
}: DocumentDetailsPanelProps) => {
  // State for inline editing
  const [editingField, setEditingField] = useState<"documentType" | "accessLevel" | null>(null);
  const [editValues, setEditValues] = useState({
    documentType: document.documentType || "",
    accessLevel: document.accessLevel || "",
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Create options for dropdowns
  const documentTypeOptions = createOptionsFromEnum(ContractsV1Files.DocumentTypeSchema.enum);
  const accessLevelOptions = createOptionsFromEnum(ContractsV1Files.FileAccessLevelSchema.enum);

  const handleStartEdit = (field: "documentType" | "accessLevel") => {
    setEditingField(field);
    setEditValues({
      documentType: document.documentType || "",
      accessLevel: document.accessLevel || "",
    });
  };

  const handleSave = (field: "documentType" | "accessLevel") => {
    const newValue = editValues[field];
    if (newValue && newValue !== document[field]) {
      onLocalUpdate?.({
        id: document.id,
        [field]: newValue,
      });
    }
    setEditingField(null);
  };

  const handleCancel = () => {
    setEditValues({
      documentType: document.documentType || "",
      accessLevel: document.accessLevel || "",
    });
    setEditingField(null);
  };

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case "public":
        return "bg-green-50 text-green-700 ring-green-600/20";
      case "internal":
        return "bg-yellow-50 text-yellow-700 ring-yellow-600/20";
      case "confidential":
        return "bg-orange-50 text-orange-700 ring-orange-600/20";
      case "restricted":
        return "bg-red-50 text-red-700 ring-red-600/20";
      default:
        return "bg-gray-50 text-gray-700 ring-gray-600/20";
    }
  };

  return (
    <div className={className}>
      <Card className="h-full flex flex-col">
        <CardHeader className="pb-3 flex-shrink-0">
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 flex-1 overflow-y-auto">
          {/* Basic Information */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Basic Information
            </h4>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <FileText className="h-4 w-4 mt-0.5 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium break-words">{document.name}</p>
                  <p className="text-xs text-muted-foreground">File name</p>
                </div>
              </div>

              {document.notes && (
                <div className="flex items-start gap-2">
                  <Tag className="h-4 w-4 mt-0.5 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm break-words">{document.notes}</p>
                    <p className="text-xs text-muted-foreground">Notes</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* File Properties */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              File Properties
            </h4>
            <div className="grid grid-rows-2 gap-3">
              {/* Document Type Field */}
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Type</p>
                {editingField === "documentType" ? (
                  <div className="flex items-center gap-1">
                    <Select
                      value={editValues.documentType}
                      onValueChange={(value) =>
                        setEditValues((prev) => ({
                          ...prev,
                          documentType: value,
                        }))
                      }
                    >
                      <SelectTrigger className="h-6 text-xs min-w-[120px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="min-w-[140px]">
                        {documentTypeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value} className="text-xs">
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleSave("documentType")}
                      className="h-4 w-4 p-0 hover:bg-green-100 hover:text-green-600"
                    >
                      <Check className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleCancel}
                      className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-1 group">
                    <Badge variant="secondary" className="text-xs">
                      {document.documentType || "Unknown"}
                    </Badge>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleStartEdit("documentType")}
                      className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-accent"
                    >
                      <Pencil className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>

              {/* Access Level Field */}
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Access Level</p>
                {editingField === "accessLevel" ? (
                  <div className="flex items-center gap-1">
                    <Select
                      value={editValues.accessLevel}
                      onValueChange={(value) =>
                        setEditValues((prev) => ({
                          ...prev,
                          accessLevel: value,
                        }))
                      }
                    >
                      <SelectTrigger className="h-6 text-xs min-w-[120px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="min-w-[140px]">
                        {accessLevelOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value} className="text-xs">
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleSave("accessLevel")}
                      className="h-4 w-4 p-0 hover:bg-green-100 hover:text-green-600"
                    >
                      <Check className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleCancel}
                      className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-1 group">
                    <Badge
                      variant="outline"
                      className={`text-xs ring-1 ring-inset ${
                        document.accessLevel
                          ? getAccessLevelColor(document.accessLevel)
                          : "bg-gray-50 text-gray-700 ring-gray-600/20"
                      }`}
                    >
                      {document.accessLevel || "Not set"}
                    </Badge>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleStartEdit("accessLevel")}
                      className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-accent"
                    >
                      <Pencil className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Timestamps */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Timestamps
            </h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm">{formatDate(document.createdAt)}</p>
                  <p className="text-xs text-muted-foreground">Created</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm">{formatDate(document.updatedAt)}</p>
                  <p className="text-xs text-muted-foreground">Last modified</p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Additional Information */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Additional Information
            </h4>
            <div className="space-y-2">
              {/* Tags */}
              <div className="flex items-start gap-2">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">Tags</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {(document.tags ?? []).length === 0 ? (
                      <span className="text-xs text-muted-foreground">None</span>
                    ) : (
                      (document.tags ?? []).map((tag, index) => (
                        <Badge key={`${tag}-${index}`} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))
                    )}
                  </div>
                </div>
              </div>

              {document.createdBy && (
                <div className="flex items-start gap-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">Created By</p>
                    <p className="text-xs text-muted-foreground">
                      {document.createdBy.name || document.createdBy.email || "Unknown"}
                    </p>
                  </div>
                </div>
              )}

              {document.scope && (
                <div className="flex items-start gap-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">Scope</p>
                    <Badge variant="outline" className="text-xs">
                      {document.scope.name}
                    </Badge>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Actions */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Actions
            </h4>
            <div className="flex flex-col gap-2">
              <Button variant="outline" size="sm" className="justify-start">
                <DatabaseIcon className="h-4 w-4 mr-2" />
                Generate Embeddings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
