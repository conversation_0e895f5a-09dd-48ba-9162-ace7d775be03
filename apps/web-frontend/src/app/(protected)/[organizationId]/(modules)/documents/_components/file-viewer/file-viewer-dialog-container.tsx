"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import PdfViewerIframe from "./pdf-viewer-iframe";
import DocxViewer from "./docx-viewer";
import { useState } from "react";
import { Eye, Loader2 } from "lucide-react";
import { getSingleFileStream } from "../../_actions/get-single-file-stream";
import { useParams } from "next/navigation";

interface FileViewerDialogContainerProps {
  fileId: string;
  fileName?: string;
}

type FileType = "pdf" | "image" | "docx" | "txt" | "md" | "unsupported";

// File type detection utilities
const detectFileType = (arrayBuffer: ArrayBuffer): FileType => {
  const uint8Array = new Uint8Array(arrayBuffer);
  const header = Array.from(uint8Array.slice(0, 4))
    .map((b) => String.fromCharCode(b))
    .join("");

  console.log("File type detection:", {
    arrayBufferSize: arrayBuffer.byteLength,
    header,
    firstBytes: Array.from(uint8Array.slice(0, 10)),
  });

  // PDF detection
  if (header === "%PDF") {
    console.log("Detected as PDF");
    return "pdf";
  }

  // Image detection (JPEG, PNG, GIF)
  if (
    header.startsWith("\xFF\xD8\xFF") || // JPEG
    header.startsWith("\x89PNG") || // PNG
    header.startsWith("GIF8")
  ) {
    console.log("Detected as image");
    return "image";
  }

  // DOCX detection (ZIP-based format)
  if (header.startsWith("PK")) {
    console.log("Detected as DOCX");
    return "docx";
  }

  // TXT detection
  if (header.startsWith("TXT") || header.startsWith("CSV")) {
    console.log("Detected as TXT");
    return "txt";
  }

  // MD detection
  if (header.startsWith("MD")) {
    console.log("Detected as MD");
    return "md";
  }

  console.log("Detected as unsupported");
  return "unsupported";
};

const getFileTypeError = (fileType: FileType): string | null => {
  switch (fileType) {
    case "unsupported":
      return "File type is not supported for viewing";
    default:
      return null;
  }
};

const renderFileViewer = (fileType: FileType, arrayBuffer: ArrayBuffer) => {
  console.log("Rendering file viewer for type:", fileType);

  switch (fileType) {
    case "pdf":
      console.log("Rendering PDF viewer");
      return <PdfViewerIframe arrayBuffer={arrayBuffer} />;
    case "image":
      console.log("Rendering image viewer");
      return (
        <div className="flex justify-center">
          <img
            src={URL.createObjectURL(new Blob([arrayBuffer]))}
            alt="Preview"
            className="max-w-full h-auto"
          />
        </div>
      );
    case "docx":
      console.log("Rendering DOCX viewer");
      return <DocxViewer arrayBuffer={arrayBuffer} />;
    case "txt":
      console.log("Rendering TXT viewer");
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">TXT viewer not implemented yet</div>
        </div>
      );
    case "md":
      console.log("Rendering MD viewer");
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">MD viewer not implemented yet</div>
        </div>
      );
    case "unsupported":
      console.log("Rendering unsupported viewer");
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">File type not supported</div>
        </div>
      );
    default:
      console.log("Rendering default (null)");
      return null;
  }
};

export default function FileViewerDialogContainer({
  fileId,
  fileName,
}: FileViewerDialogContainerProps) {
  const [open, setOpen] = useState(false);
  const [arrayBuffer, setArrayBuffer] = useState<ArrayBuffer | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileType, setFileType] = useState<FileType | null>(null);
  const params = useParams();
  const organizationId = params.organizationId as string;

  const loadFile = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getSingleFileStream(organizationId, fileId);

      if (!result.success || !result.data) {
        setError(result.error || "Failed to load file");
        return;
      }

      const detectedFileType = detectFileType(result.data);
      const fileTypeError = getFileTypeError(detectedFileType);

      if (fileTypeError) {
        setError(fileTypeError);
        return;
      }

      setArrayBuffer(result.data);
      setFileType(detectedFileType);
    } catch (err) {
      console.error("File viewer error:", err);
      setError("Failed to load file");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = async (open: boolean) => {
    setOpen(open);
    if (open && !arrayBuffer) {
      await loadFile();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <button className="inline-flex items-center justify-center h-8 w-8 p-0 hover:bg-accent/50 hover:text-accent-foreground rounded-sm">
          <Eye className="h-4 w-4 text-muted-foreground" />
        </button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[95vw] sm:max-h-[95vh] w-[95vw] h-[95vh]">
        <DialogHeader>
          <DialogTitle className="text-lg">{fileName || "File Viewer"}</DialogTitle>
        </DialogHeader>
        {loading && (
          <div className="flex flex-col items-center justify-center h-64 gap-2">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading file...</span>
          </div>
        )}
        {error && (
          <div className="flex flex-col items-center justify-center h-64 gap-2">
            <div className="text-destructive text-lg font-medium">Failed to load file</div>
            <div className="text-muted-foreground text-sm max-w-md">{error}</div>
          </div>
        )}
        {arrayBuffer && !loading && !error && fileType && renderFileViewer(fileType, arrayBuffer)}
      </DialogContent>
    </Dialog>
  );
}
