"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Dropzone, DropzoneEmptyState, DropzoneContent } from "@/components/ui/shadcn-io/dropzone";
import { UploadFileIcon } from "./document-type-icons";
import { useDocumentsContext } from "../documents-provider";
import { toast } from "sonner";
import { uploadBase64 } from "../../_actions/upload-file";
import { CheckIcon } from "lucide-react";
import { isTextEditable } from "@/lib/documents/is-text-editable";

interface UploadFileCarouselItemProps {
  className?: string;
}

function toBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // Strip data: prefix if present
      const idx = result.indexOf(",");
      resolve(idx >= 0 ? result.slice(idx + 1) : result);
    };
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
}

async function sha256HexFromArrayBuffer(buffer: ArrayBuffer): Promise<string> {
  const digest = await crypto.subtle.digest("SHA-256", buffer);
  const hex = Array.from(new Uint8Array(digest))
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
  return hex;
}

export const UploadFileCarouselItem: React.FC<UploadFileCarouselItemProps> = ({ className }) => {
  const {
    selectedFiles,
    addSelectedFiles,
    clearSelectedFiles,
    organizationId,
    requestRefresh,
    addDocumentLocal,
  } = useDocumentsContext();

  const [isSuccess, setIsSuccess] = React.useState(false);

  const handleFileDrop = async (acceptedFiles: File[]) => {
    if (!acceptedFiles || acceptedFiles.length === 0) return;

    // Phase 2: auto-upload first file (keep simple now; queue support can follow)
    const first = acceptedFiles[0];
    if (!(first instanceof File)) return;
    const file: File = first;

    clearSelectedFiles();
    addSelectedFiles([file]);

    try {
      const rawBuffer = await file.arrayBuffer();
      const checksum = await sha256HexFromArrayBuffer(rawBuffer);
      const bufferBase64 = await toBase64(file);

      // Attempt lightweight client-side text extraction for simple formats
      let extractedContent: string | undefined;
      const editable = isTextEditable({ name: file.name, fileType: file.type });
      if (editable) {
        const ext = file.name.split(".").pop()?.toLowerCase();
        if (ext === "txt" || ext === "md" || ext === "html") {
          try {
            // Read as text via Blob API
            extractedContent = await file.text();
            // For markdown: keep raw text; editor will accept it as initial content
            // For html: keep as-is; backend/editor can sanitize/render accordingly
          } catch {
            // non-blocking: fall back to server-side processing if any
            extractedContent = undefined;
          }
        }
      }

      const res = await uploadBase64({
        organizationId,
        name: file.name,
        bufferBase64,
        checksum,
        content: extractedContent,
      });
      if (!res.success || !res.file) {
        toast.error(res.message || "Upload failed");
        return;
      }

      // Update local list and trigger refresh
      addDocumentLocal(res.file);
      requestRefresh();

      // Brief success checkmark before reset
      setIsSuccess(true);
      setTimeout(() => {
        setIsSuccess(false);
        clearSelectedFiles();
      }, 900);

      toast.success(`Uploaded "${file.name}"`);
    } catch (error) {
      console.error({ error });
      toast.error("Upload error");
    }
  };

  const handleError = (error: Error) => {
    toast.error(`File selection error: ${error.message}`);
  };

  const hasSelectedFiles = selectedFiles.length > 0;

  return (
    <Card
      className={cn(
        "relative h-full overflow-hidden p-0 transition-colors", // remove default card padding
        // Card shows state using a subtle background and dashed border accent
        "border-2 border-dashed border-muted-foreground/30",
        "hover:border-muted-foreground/50 hover:bg-muted/10",
        (hasSelectedFiles || isSuccess) &&
          "border-purple-400/50 bg-purple-50/40 dark:bg-purple-950/30",
        className,
      )}
    >
      {/* Establish a sizing context so the absolute Dropzone can fill it */}
      <CardContent className="relative h-full min-h-[220px] p-0">
        <Dropzone
          aria-label="Upload files"
          aria-describedby="upload-instructions"
          // Make the Dropzone button cover the entire card area
          className={cn(
            "absolute inset-0 h-full w-full",
            // Remove default Button visuals so only the card styling is visible
            "rounded-none border-none bg-transparent !p-0 shadow-none",
            // Center content
            "flex items-center justify-center",
            // Remove focus rings that conflict with card outline
            "focus:ring-0 focus-visible:ring-0",
          )}
          // Wire the current selection so Dropzone can render selected state content
          src={hasSelectedFiles ? selectedFiles.map((f) => f.file) : undefined}
          accept={{
            "application/pdf": [".pdf"],
            "application/msword": [".doc"],
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
            "text/plain": [".txt"],
            "text/markdown": [".md"],
            "text/html": [".html"],
            "application/vnd.ms-excel": [".xls"],
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
            "application/vnd.ms-powerpoint": [".ppt"],
            "application/vnd.openxmlformats-officedocument.presentationml.presentation": [".pptx"],
          }}
          maxFiles={2}
          maxSize={10 * 1024 * 1024} // 10MB
          onDrop={handleFileDrop}
          onError={handleError}
        >
          {isSuccess ? (
            <div className="flex-1 p-6 text-center">
              <div className="flex h-full flex-col items-center justify-center space-y-3">
                <div className="flex size-12 items-center justify-center rounded-full bg-green-100 text-green-700 dark:bg-green-900/40 dark:text-green-300">
                  <CheckIcon size={28} />
                </div>
                <div className="text-center">
                  <h3 className="font-medium text-sm text-foreground">Uploaded</h3>
                  <p className="text-muted-foreground text-xs mt-1">Saved successfully</p>
                </div>
              </div>
            </div>
          ) : hasSelectedFiles ? (
            <DropzoneContent className="flex-1 p-6 text-center">
              <div className="flex h-full flex-col items-center justify-center space-y-3">
                <UploadFileIcon size={48} className="mb-1" />
                <div className="text-center">
                  <h3 className="font-medium text-sm text-foreground">
                    {selectedFiles.length === 1
                      ? selectedFiles[0]!.file.name
                      : `${selectedFiles.length} files selected`}
                  </h3>
                  <p id="upload-instructions" className="text-muted-foreground text-xs mt-1">
                    Uploading... Please wait
                  </p>
                </div>
              </div>
            </DropzoneContent>
          ) : (
            <DropzoneEmptyState className="flex-1 p-6 text-center">
              <div className="flex h-full flex-col items-center justify-center space-y-3">
                <UploadFileIcon size={48} className="mb-1" />
                <div className="text-center">
                  <h3 className="font-medium text-sm text-foreground">Upload File</h3>
                  <p id="upload-instructions" className="text-muted-foreground text-xs mt-1">
                    Drag & drop or click to select
                  </p>
                  <p className="text-muted-foreground/80 text-xs mt-1">
                    PDF, DOC, TXT, MD, XLS, PPT (max 10MB)
                  </p>
                </div>
              </div>
            </DropzoneEmptyState>
          )}
        </Dropzone>
      </CardContent>
    </Card>
  );
};
