"use server";

import { api<PERSON><PERSON><PERSON><PERSON><PERSON>, ApiError } from "@/lib/backend-api";

export interface DeleteDocumentsParams {
  organizationId: string;
  fileIds: string[];
}

export interface DeleteDocumentsResponse {
  success: boolean;
  message?: string;
  deletedCount?: number;
}

export async function deleteDocuments(
  params: DeleteDocumentsParams,
): Promise<DeleteDocumentsResponse> {
  const { organizationId, fileIds } = params;

  if (!fileIds || fileIds.length === 0) {
    return {
      success: false,
      message: "No files selected for deletion",
    };
  }

  const endpoint = `/v1/organizations/${organizationId}/files`;

  try {
    // The backend expects { ids: string[] } in the request body
    const requestBody = { ids: fileIds };

    await apiRequestJson<boolean>(endpoint, {
      method: "DELETE",
      body: JSON.stringify(requestBody),
    });

    return {
      success: true,
      message: `Successfully deleted ${fileIds.length} file${fileIds.length > 1 ? "s" : ""}`,
      deletedCount: fileIds.length,
    };
  } catch (error) {
    console.error("Error deleting documents:", error);

    if (error instanceof ApiError) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: "Unexpected error while deleting documents",
    };
  }
}
