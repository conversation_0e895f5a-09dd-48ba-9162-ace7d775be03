"use client";

import React from "react";
import { DocumentsDataTable } from "./documents-data-table";
import { CreateDocumentCarousel } from "./create-document";
import { ContractsV1Files } from "@askinfosec/types";
import { DocumentsProvider, useDocumentsContext } from "./documents-provider";

interface DocumentsManagerProps {
  organizationId: string;
}

export const DocumentsManager: React.FC<DocumentsManagerProps> = ({ organizationId }) => {
  return (
    <DocumentsProvider organizationId={organizationId}>
      <CreateDocumentHarness organizationId={organizationId} />
      <DocumentsDataTable organizationId={organizationId} />
    </DocumentsProvider>
  );
};

function CreateDocumentHarness({ organizationId }: { organizationId: string }) {
  const { addDocumentLocal, openDocumentLocal } = useDocumentsContext();

  const handleDocumentCreated = (document: ContractsV1Files.FileDTO) => {
    addDocumentLocal(document);
    setTimeout(() => {
      openDocumentLocal(document.id);
    }, 50);
  };

  return (
    <CreateDocumentCarousel
      organizationId={organizationId}
      onDocumentCreated={handleDocumentCreated}
    />
  );
}
