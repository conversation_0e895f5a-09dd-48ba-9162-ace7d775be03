"use client";

import React, { useEffect, useRef, useState } from "react";
import { renderAsync } from "docx-preview";

interface DocxViewerProps {
  arrayBuffer: ArrayBuffer;
}

const DocxViewer = ({ arrayBuffer }: DocxViewerProps) => {
  console.log("DocxViewer component rendered with arrayBuffer:", arrayBuffer?.byteLength);

  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const renderDocx = async () => {
      if (!containerRef.current) return;

      try {
        setIsLoading(true);
        setError(null);

        // Create blob from ArrayBuffer
        const uint8Array = new Uint8Array(arrayBuffer);
        const blob = new Blob([uint8Array], {
          type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        });

        // Clear container
        containerRef.current.innerHTML = "";

        // Render DOCX with timeout
        const renderPromise = renderAsync(blob, containerRef.current, containerRef.current, {
          ignoreWidth: true,
          ignoreHeight: true,
          ignoreFonts: false,
          breakPages: true,
          debug: true,
        });

        // Add timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("DOCX render timeout after 10 seconds")), 10000);
        });

        await Promise.race([renderPromise, timeoutPromise]);

        setIsLoading(false);
      } catch (err) {
        setError(
          `Failed to load DOCX document: ${err instanceof Error ? err.message : "Unknown error"}`,
        );
        setIsLoading(false);
      }
    };

    renderDocx();
  }, [arrayBuffer]);

  return (
    <div className="h-full w-full overflow-auto bg-background">
      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Loading DOCX document...</div>
        </div>
      )}

      {error && (
        <div className="flex items-center justify-center h-64">
          <div className="text-destructive">{error}</div>
        </div>
      )}

      <div
        ref={containerRef}
        className="min-h-full p-4"
        style={{
          fontFamily: "system-ui, -apple-system, sans-serif",
          lineHeight: "1",
          display: isLoading || error ? "none" : "block",
        }}
      />
    </div>
  );
};

export default DocxViewer;
