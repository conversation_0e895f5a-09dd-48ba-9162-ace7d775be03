"use server";

import { apiGet } from "@/lib/backend-api";
import { ContractsV1Files } from "@askinfosec/types";
import { Buff<PERSON> } from "buffer";

interface DownloadDocumentResult {
  success: boolean;
  error?: string;
  data?: {
    file: ContractsV1Files.FileDTO;
    buffer: string; // Base64 encoded string
    filename: string;
  };
}

export async function downloadDocument(
  organizationId: string,
  fileId: string,
): Promise<DownloadDocumentResult> {
  try {
    // Streamed binary response; do NOT parse as JSON
    const res = await apiGet(`/v1/organizations/${organizationId}/files/${fileId}/download`);
    if (!res.ok) {
      return { success: false, error: `Request failed: ${res.status}` };
    }

    // Read as ArrayBuffer and convert to base64
    const arrayBuffer = await res.arrayBuffer();
    const base64 = Buffer.from(arrayBuffer).toString("base64");

    // Extract metadata from headers if available
    const contentDisposition = res.headers.get("content-disposition") || "";
    const contentType = res.headers.get("content-type") || "application/octet-stream";
    const filenameMatch = /filename="?([^";]+)"?/i.exec(contentDisposition || "");
    const filenameFromHeader = filenameMatch?.[1];

    // Build a minimal file DTO for downstream usage (without buffer)
    const fileMeta: ContractsV1Files.FileDTO = {
      id: fileId,
      name: filenameFromHeader || "document",
      fileType: contentType,
      organizationId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    } as ContractsV1Files.FileDTO;

    return {
      success: true,
      data: {
        file: fileMeta,
        buffer: base64,
        filename: filenameFromHeader || fileMeta.name || "document",
      },
    };
  } catch (error) {
    console.error("Error downloading document:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}
