"use client";

import React from "react";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Trash2 } from "lucide-react";
import { ContractsV1Files } from "@askinfosec/types";

interface DeleteButtonProps {
  file: ContractsV1Files.FileDTO;
  onRequestDelete: (file: ContractsV1Files.FileDTO) => void;
}

export function DeleteButton({ file, onRequestDelete }: DeleteButtonProps) {
  return (
    <DropdownMenuItem onSelect={() => onRequestDelete(file)} className="text-red-600">
      <Trash2 className="mr-2 h-4 w-4" />
      Delete
    </DropdownMenuItem>
  );
}
