import { getServerSession } from "@/lib/auth-server";
import { redirect } from "next/navigation";
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  Shield,
  FileText,
  Activity,
  Zap,
  Download,
  Filter,
  Refresh<PERSON>w,
  <PERSON>lipboard<PERSON>ist,
  Settings,
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PageMetadataSetter } from "@/components/layout/page-metadata-setter";

// Mock data for demonstration - in real implementation, this would come from your database
const mockData = {
  compliance: {
    overallScore: 87,
    trend: "up",
    frameworks: [
      { name: "ISO 27001", score: 92, status: "compliant" },
      { name: "SOC 2", score: 85, status: "compliant" },
      { name: "GDPR", score: 78, status: "at-risk" },
      { name: "HIPAA", score: 91, status: "compliant" },
      { name: "PCI DSS", score: 82, status: "compliant" },
    ],
    recentFindings: [
      {
        id: 1,
        severity: "high",
        description: "Access control gaps in cloud infrastructure",
        status: "open",
      },
      {
        id: 2,
        severity: "medium",
        description: "Documentation updates required for SOC 2",
        status: "in-progress",
      },
      {
        id: 3,
        severity: "low",
        description: "Minor policy updates needed",
        status: "resolved",
      },
    ],
  },
  security: {
    riskScore: 23,
    trend: "down",
    incidents: {
      total: 12,
      critical: 1,
      high: 3,
      medium: 5,
      low: 3,
    },
    vulnerabilities: {
      critical: 2,
      high: 8,
      medium: 15,
      low: 32,
    },
    threats: [
      { type: "Phishing", count: 45, trend: "up" },
      { type: "Malware", count: 12, trend: "down" },
      { type: "DDoS", count: 3, trend: "stable" },
      { type: "Insider Threat", count: 2, trend: "up" },
    ],
  },
  risk: {
    overallRisk: "medium",
    riskDistribution: {
      critical: 3,
      high: 12,
      medium: 28,
      low: 45,
    },
    topRisks: [
      {
        id: 1,
        name: "Cloud Security Configuration",
        likelihood: "high",
        impact: "high",
        status: "mitigating",
      },
      {
        id: 2,
        name: "Third-party Vendor Risk",
        likelihood: "medium",
        impact: "high",
        status: "monitoring",
      },
      {
        id: 3,
        name: "Data Privacy Compliance",
        likelihood: "high",
        impact: "medium",
        status: "addressing",
      },
    ],
  },
  audit: {
    upcoming: [
      { id: 1, type: "SOC 2 Type II", date: "2024-03-15", status: "scheduled" },
      {
        id: 2,
        type: "ISO 27001 Surveillance",
        date: "2024-04-20",
        status: "preparing",
      },
      {
        id: 3,
        type: "Internal Security Review",
        date: "2024-03-30",
        status: "in-progress",
      },
    ],
    recent: [
      {
        id: 1,
        type: "PCI DSS Assessment",
        date: "2024-02-15",
        result: "passed",
        findings: 3,
      },
      {
        id: 2,
        type: "GDPR Compliance Review",
        date: "2024-01-30",
        result: "conditional",
        findings: 7,
      },
    ],
  },
  performance: {
    metrics: [
      {
        name: "Mean Time to Detect (MTTD)",
        value: "2.3 hours",
        target: "< 4 hours",
        status: "good",
      },
      {
        name: "Mean Time to Respond (MTTR)",
        value: "4.1 hours",
        target: "< 6 hours",
        status: "good",
      },
      {
        name: "Mean Time to Resolve (MTTR)",
        value: "18.5 hours",
        target: "< 24 hours",
        status: "good",
      },
      {
        name: "Security Awareness Training Completion",
        value: "94%",
        target: "> 90%",
        status: "excellent",
      },
    ],
  },
};

export default async function ReportsPage() {
  const session = await getServerSession();

  if (!session) {
    redirect("/sign-in");
  }

  return (
    <>
      <PageMetadataSetter
        title="Reports & Analytics"
        description="Comprehensive GRC and security metrics dashboard"
      />
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Key Performance Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockData.compliance.overallScore}%</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {mockData.compliance.trend === "up" ? (
                  <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-600 mr-1" />
                )}
                +2.1% from last month
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Security Risk Score</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockData.security.riskScore}/100</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {mockData.security.trend === "down" ? (
                  <TrendingDown className="h-3 w-3 text-green-600 mr-1" />
                ) : (
                  <TrendingUp className="h-3 w-3 text-red-600 mr-1" />
                )}
                -5.2% from last month
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Active Incidents</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockData.security.incidents.total}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <span className="text-red-600 font-medium">
                  {mockData.security.incidents.critical} critical
                </span>
                <span className="mx-1">•</span>
                <span className="text-orange-600">{mockData.security.incidents.high} high</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Open Vulnerabilities</CardTitle>
                <XCircle className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockData.security.vulnerabilities.critical +
                  mockData.security.vulnerabilities.high}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <span className="text-red-600 font-medium">
                  {mockData.security.vulnerabilities.critical} critical
                </span>
                <span className="mx-1">•</span>
                <span className="text-orange-600">
                  {mockData.security.vulnerabilities.high} high
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Compliance Framework Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                  Compliance Framework Status
                </CardTitle>
                <CardDescription>Current compliance scores across all frameworks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockData.compliance.frameworks.map((framework) => (
                    <div
                      key={framework.name}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span className="font-medium">{framework.name}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <div className="font-semibold">{framework.score}%</div>
                          <div className="text-xs text-gray-500">
                            {framework.status === "compliant"
                              ? "Compliant"
                              : framework.status === "at-risk"
                                ? "At Risk"
                                : "Non-Compliant"}
                          </div>
                        </div>
                        <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              framework.score >= 90
                                ? "bg-green-500"
                                : framework.score >= 80
                                  ? "bg-yellow-500"
                                  : "bg-red-500"
                            }`}
                            style={{ width: `${framework.score}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Security Incidents Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2 text-blue-600" />
                  Security Incidents & Threats
                </CardTitle>
                <CardDescription>Recent security incidents and threat landscape</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Threat Types */}
                  <div>
                    <h4 className="font-medium mb-3">Threat Types (Last 30 Days)</h4>
                    <div className="space-y-2">
                      {mockData.security.threats.map((threat) => (
                        <div key={threat.type} className="flex items-center justify-between">
                          <span className="text-sm">{threat.type}</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{threat.count}</span>
                            {threat.trend === "up" ? (
                              <TrendingUp className="h-3 w-3 text-red-600" />
                            ) : threat.trend === "down" ? (
                              <TrendingDown className="h-3 w-3 text-green-600" />
                            ) : (
                              <div className="h-3 w-3 text-gray-400">—</div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Recent Findings */}
                  <div>
                    <h4 className="font-medium mb-3">Recent Compliance Findings</h4>
                    <div className="space-y-2">
                      {mockData.compliance.recentFindings.map((finding) => (
                        <div
                          key={finding.id}
                          className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded"
                        >
                          <div className="flex items-center space-x-2">
                            <div
                              className={`w-2 h-2 rounded-full ${
                                finding.severity === "high"
                                  ? "bg-red-500"
                                  : finding.severity === "medium"
                                    ? "bg-yellow-500"
                                    : "bg-green-500"
                              }`}
                            ></div>
                            <span className="text-sm">{finding.description}</span>
                          </div>
                          <Badge variant={finding.status === "resolved" ? "default" : "secondary"}>
                            {finding.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Risk Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-purple-600" />
                  Risk Management Overview
                </CardTitle>
                <CardDescription>Current risk landscape and mitigation status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Risk Distribution */}
                  <div>
                    <h4 className="font-medium mb-3">Risk Distribution</h4>
                    <div className="grid grid-cols-4 gap-2 text-center">
                      <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded">
                        <div className="text-lg font-bold text-red-600">
                          {mockData.risk.riskDistribution.critical}
                        </div>
                        <div className="text-xs text-gray-600">Critical</div>
                      </div>
                      <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded">
                        <div className="text-lg font-bold text-orange-600">
                          {mockData.risk.riskDistribution.high}
                        </div>
                        <div className="text-xs text-gray-600">High</div>
                      </div>
                      <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded">
                        <div className="text-lg font-bold text-yellow-600">
                          {mockData.risk.riskDistribution.medium}
                        </div>
                        <div className="text-xs text-gray-600">Medium</div>
                      </div>
                      <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded">
                        <div className="text-lg font-bold text-green-600">
                          {mockData.risk.riskDistribution.low}
                        </div>
                        <div className="text-xs text-gray-600">Low</div>
                      </div>
                    </div>
                  </div>

                  {/* Top Risks */}
                  <div>
                    <h4 className="font-medium mb-3">Top Risks</h4>
                    <div className="space-y-2">
                      {mockData.risk.topRisks.map((risk) => (
                        <div
                          key={risk.id}
                          className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded"
                        >
                          <div>
                            <div className="font-medium text-sm">{risk.name}</div>
                            <div className="text-xs text-gray-500">
                              {risk.likelihood} likelihood • {risk.impact} impact
                            </div>
                          </div>
                          <Badge variant="outline">{risk.status}</Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-yellow-600" />
                  Performance Metrics
                </CardTitle>
                <CardDescription>
                  Key security and compliance performance indicators
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockData.performance.metrics.map((metric) => (
                    <div key={metric.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{metric.name}</span>
                        <Badge variant={metric.status === "excellent" ? "default" : "secondary"}>
                          {metric.status}
                        </Badge>
                      </div>
                      <div className="text-lg font-bold">{metric.value}</div>
                      <div className="text-xs text-gray-500">Target: {metric.target}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Audit Schedule */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <ClipboardList className="h-5 w-5 mr-2 text-indigo-600" />
                  Audit Schedule
                </CardTitle>
                <CardDescription>Upcoming and recent audits</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-3">Upcoming Audits</h4>
                    <div className="space-y-2">
                      {mockData.audit.upcoming.map((audit) => (
                        <div key={audit.id} className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                          <div className="font-medium text-sm">{audit.type}</div>
                          <div className="text-xs text-gray-500">{audit.date}</div>
                          <Badge variant="outline" className="mt-1">
                            {audit.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">Recent Audits</h4>
                    <div className="space-y-2">
                      {mockData.audit.recent.map((audit) => (
                        <div key={audit.id} className="p-2 bg-gray-50 dark:bg-gray-800 rounded">
                          <div className="font-medium text-sm">{audit.type}</div>
                          <div className="text-xs text-gray-500">{audit.date}</div>
                          <div className="flex items-center justify-between mt-1">
                            <Badge variant={audit.result === "passed" ? "default" : "secondary"}>
                              {audit.result}
                            </Badge>
                            <span className="text-xs text-gray-500">{audit.findings} findings</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2 text-gray-600" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Compliance Report
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Shield className="h-4 w-4 mr-2" />
                    Security Assessment
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Target className="h-4 w-4 mr-2" />
                    Risk Assessment
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <ClipboardList className="h-4 w-4 mr-2" />
                    Schedule Audit
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer with Last Updated */}
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center justify-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>Last updated: {new Date().toLocaleString()}</span>
          </div>
          <p className="mt-1">Data refreshes automatically every 15 minutes</p>
        </div>
      </div>
    </>
  );
}
