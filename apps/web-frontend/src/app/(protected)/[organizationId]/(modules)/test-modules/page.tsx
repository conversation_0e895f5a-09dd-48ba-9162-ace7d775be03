"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PageMetadataSetter } from "@/components/layout/page-metadata-setter";
import { useOrganizationModules } from "@/providers/organization-modules-provider";
import { useFilteredNavigation } from "@/hooks/use-filtered-navigation";

export default function TestModulesPage() {
  return (
    <>
      <PageMetadataSetter
        title="Test Modules"
        description="Testing organization modules provider"
      />
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold mb-4">Organization Modules Test</h1>
          <p className="text-muted-foreground">
            This page tests the organization modules provider to ensure module access control is
            working correctly.
          </p>
        </div>

        <ModulesStatus />
        <FilteredNavigation />
      </div>
    </>
  );
}

function ModulesStatus() {
  const { modules, activeModuleIds, isLoading, error, hasModuleAccess } = useOrganizationModules();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Modules Status</CardTitle>
          <CardDescription>Loading modules...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Modules Status</CardTitle>
          <CardDescription>Error loading modules</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-red-600">{error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Modules Status</CardTitle>
        <CardDescription>
          Total modules: {modules.length} | Active modules: {activeModuleIds.size}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">All Modules:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {modules.map((module) => (
                <div
                  key={module.id}
                  className="p-3 border rounded-lg flex items-center justify-between"
                >
                  <div>
                    <div className="font-medium">{module.displayName}</div>
                    <div className="text-sm text-gray-500">{module.name}</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={module.isEnabled ? "default" : "secondary"}>
                      {module.isEnabled ? "Enabled" : "Disabled"}
                    </Badge>
                    <Badge variant="outline">{module.source}</Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Module Access Tests:</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {[
                "dashboard",
                "documents",
                "controls",
                "compliance",
                "risk-management",
                "audits",
                "trainings",
                "settings",
              ].map((moduleId) => (
                <div key={moduleId} className="p-2 border rounded text-center">
                  <div className="text-sm font-medium">{moduleId}</div>
                  <Badge
                    variant={hasModuleAccess(moduleId) ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {hasModuleAccess(moduleId) ? "Access" : "No Access"}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function FilteredNavigation() {
  const { filteredRoutes, filteredSettingsRoute, isLoading } = useFilteredNavigation();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Filtered Navigation</CardTitle>
          <CardDescription>Loading navigation...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Filtered Navigation</CardTitle>
        <CardDescription>
          Navigation items filtered based on module access: {filteredRoutes.length} routes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Available Routes:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {filteredRoutes.map((route) => (
                <div key={route.id} className="p-3 border rounded-lg">
                  <div className="font-medium">{route.name}</div>
                  <div className="text-sm text-gray-500">{route.id}</div>
                  <div className="text-xs text-gray-400">{route.path}</div>
                </div>
              ))}
            </div>
          </div>

          {filteredSettingsRoute && (
            <div>
              <h3 className="font-semibold mb-2">Settings Route:</h3>
              <div className="p-3 border rounded-lg">
                <div className="font-medium">{filteredSettingsRoute.name}</div>
                <div className="text-sm text-gray-500">{filteredSettingsRoute.id}</div>
                <div className="text-xs text-gray-400">{filteredSettingsRoute.path}</div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
