"use client";

/* eslint-disable @typescript-eslint/no-namespace */
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ContractsV1Products } from "@askinfosec/types";
import Script from "next/script";
import { useState } from "react";

// Declare the custom Stripe buy button element
declare global {
  namespace JSX {
    interface IntrinsicElements {
      "stripe-buy-button": {
        "buy-button-id": string;
        "publishable-key": string;
      };
    }
  }
}

interface ProductCardProps {
  product: ContractsV1Products.ProductWithModulesDTO;
  onCancelSubscription: (subscriptionId: string) => Promise<void>;
  onResumeSubscription: (subscriptionId: string) => Promise<void>;
  onSubscribe: (productId: string) => void;
  stripeCustomerSession: string | null;
  publishableKey: string | null;
}

export function ProductCard({
  product,
  onCancelSubscription,
  onResumeSubscription,
  onSubscribe,
  stripeCustomerSession,
  publishableKey,
}: ProductCardProps) {
  const isEnterprise = product.name.toLowerCase().includes("enterprise");
  const [isLoading, setIsLoading] = useState(false);

  // Helper function to calculate remaining days
  const getRemainingDays = (endDate: Date) => {
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  // Helper function to format end date
  const formatEndDate = (endDate: Date) => {
    return endDate.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Handler for cancel subscription with loading state
  const handleCancelSubscription = async (subscriptionId: string) => {
    setIsLoading(true);
    try {
      await onCancelSubscription(subscriptionId);
    } finally {
      setIsLoading(false);
    }
  };

  // Handler for resume subscription with loading state
  const handleResumeSubscription = async (subscriptionId: string) => {
    setIsLoading(true);
    try {
      await onResumeSubscription(subscriptionId);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Script src="https://js.stripe.com/v3/buy-button.js" strategy="lazyOnload" async />
      <Card className="group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 border-border/50 hover:border-primary/20 flex flex-col h-full">
        {isEnterprise && (
          <div className="absolute top-4 right-4 z-10">
            <Badge
              variant="secondary"
              className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 shadow-lg"
            >
              Enterprise
            </Badge>
          </div>
        )}

        <CardHeader className="space-y-3 pb-4">
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
              {product.name}
            </h3>
            <p className="text-sm text-muted-foreground leading-relaxed">{product.description}</p>
          </div>
        </CardHeader>

        <CardContent className="space-y-4 flex-1 flex flex-col">
          {/* Price Information */}
          {product.stripeSubscriptions.length > 0 && (
            <div className="space-y-2">
              <div className="text-2xl font-bold text-foreground">
                ${((product.stripeSubscriptions[0]?.price ?? 0) / 100).toFixed(2)}
              </div>
              <div className="text-sm text-muted-foreground">
                per {product.stripeSubscriptions[0]?.billingPeriod}
              </div>
            </div>
          )}

          <div className="pt-4 border-t border-border/50 mt-auto">
            {(() => {
              const hasSubscription = product.stripeSubscriptions.length > 0;
              const hasActiveSubscription =
                hasSubscription &&
                product.stripeSubscriptions.some(
                  (sub) => sub.status === "active" || sub.status === "canceled",
                );
              const hasPricingInfo =
                hasSubscription &&
                product.stripeSubscriptions.some(
                  (sub) => !sub.status || sub.status === "available",
                );

              if (hasActiveSubscription) {
                const subscription = product.stripeSubscriptions.find(
                  (sub) => sub.status === "active" || sub.status === "canceled",
                )!;
                const isCanceling = subscription.cancelAtPeriodEnd;
                const remainingDays = subscription.currentPeriodEnd
                  ? getRemainingDays(subscription.currentPeriodEnd)
                  : 0;
                const endDate = subscription.currentPeriodEnd
                  ? formatEndDate(subscription.currentPeriodEnd)
                  : "";

                return (
                  <div className="space-y-3">
                    {/* Subscription Status */}
                    <div className="text-center">
                      {isCanceling ? (
                        <div className="space-y-1">
                          <Badge variant="destructive" className="mb-2">
                            Canceling
                          </Badge>
                          <p className="text-sm text-muted-foreground">
                            Active until {endDate || "Date not available"}
                          </p>
                          {remainingDays > 0 && (
                            <p className="text-xs text-muted-foreground">
                              {remainingDays} day
                              {remainingDays !== 1 ? "s" : ""} remaining
                            </p>
                          )}
                        </div>
                      ) : (
                        <div className="space-y-1">
                          <Badge variant="default" className="mb-2">
                            Active
                          </Badge>
                          <p className="text-sm text-muted-foreground">
                            Next billing: {endDate || "Date not available"}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-2">
                      {isCanceling ? (
                        <Button
                          onClick={() => handleResumeSubscription(subscription.id)}
                          disabled={isLoading}
                          className="w-full bg-green-600 hover:bg-green-700 text-white transition-all duration-200"
                        >
                          {isLoading ? "Resuming..." : "Resume Subscription"}
                        </Button>
                      ) : (
                        <Button
                          onClick={() => handleCancelSubscription(subscription.id)}
                          disabled={isLoading}
                          className="w-full bg-red-600 hover:bg-red-700 text-white transition-all duration-200"
                          variant="destructive"
                        >
                          {isLoading ? "Canceling..." : "Cancel Subscription"}
                        </Button>
                      )}
                    </div>
                  </div>
                );
              }

              // Handle products with pricing information but no active subscriptions
              if (hasPricingInfo) {
                const pricingInfo = product.stripeSubscriptions.find(
                  (sub) => !sub.status || sub.status === "available",
                )!;

                return (
                  <div className="space-y-3">
                    {/* Pricing Display */}
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">
                        ${(pricingInfo.price / 100).toFixed(2)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        per {pricingInfo.billingPeriod}
                      </div>
                    </div>

                    {/* Action Button */}
                    <div className="space-y-2">
                      <Button
                        onClick={() => onSubscribe(product.id)}
                        className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-200"
                        variant={isEnterprise ? "default" : "outline"}
                        disabled={!product.isActive}
                      >
                        {isEnterprise ? <>🚀 Contact Sales</> : <>📞 Contact Us</>}
                      </Button>
                    </div>
                  </div>
                );
              }

              // Use the buy button ID from the backend
              const buyButtonId = product.stripeBuyButtonId;

              if (buyButtonId) {
                return (
                  <div
                    className="flex justify-center w-full"
                    dangerouslySetInnerHTML={{
                      __html: `<stripe-buy-button
                        buy-button-id="${buyButtonId}"
                        publishable-key="${publishableKey}"
                        customer-session-client-secret="${stripeCustomerSession}"
                      ></stripe-buy-button>`,
                    }}
                  />
                );
              }

              // Only show Contact Us button for products available for subscription (no existing subscriptions)
              return (
                <Button
                  onClick={() => onSubscribe(product.id)}
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-200"
                  variant={isEnterprise ? "default" : "outline"}
                  disabled={!product.isActive}
                >
                  {isEnterprise ? <>🚀 Contact Sales</> : <>📞 Contact Us</>}
                </Button>
              );
            })()}
          </div>
        </CardContent>

        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      </Card>
    </>
  );
}
