"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";

export function ProductListSkeleton() {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <div className="h-8 w-80 bg-muted animate-pulse rounded mx-auto" />
        <div className="h-4 w-96 bg-muted animate-pulse rounded mx-auto" />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, index) => (
          <Card key={index} className="relative overflow-hidden flex flex-col h-full">
            <CardHeader className="space-y-3 pb-4">
              <div className="space-y-2">
                <div className="h-6 w-3/4 bg-muted animate-pulse rounded" />
                <div className="h-4 w-full bg-muted animate-pulse rounded" />
                <div className="h-4 w-2/3 bg-muted animate-pulse rounded" />
              </div>
            </CardHeader>
            <CardContent className="space-y-4 flex-1 flex flex-col">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 bg-muted animate-pulse rounded-full" />
                  <div className="h-3 w-12 bg-muted animate-pulse rounded" />
                </div>
                <div className="h-5 w-16 bg-muted animate-pulse rounded" />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="h-3 w-12 bg-muted animate-pulse rounded" />
                  <div className="h-4 w-4 bg-muted animate-pulse rounded" />
                </div>
                <div className="space-y-1">
                  <div className="h-3 w-20 bg-muted animate-pulse rounded" />
                  <div className="h-4 w-4 bg-muted animate-pulse rounded" />
                </div>
              </div>

              <div className="pt-4 border-t border-border/50 mt-auto">
                <div className="h-9 w-full bg-muted animate-pulse rounded" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
