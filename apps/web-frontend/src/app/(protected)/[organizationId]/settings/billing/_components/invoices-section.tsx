import { FileText, Download, CheckCircle, Clock, XCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Invoice } from "@/types/billing";

interface InvoicesSectionProps {
  invoices: Invoice[];
}

function formatCurrency(amount: number, currency: string): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase(),
  }).format(amount / 100); // Stripe amounts are in cents
}

function formatDate(timestamp: number): string {
  return new Date(timestamp * 1000).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

function getStatusBadge(status: string | null) {
  if (!status) return null;

  const statusConfig = {
    paid: {
      variant: "default" as const,
      color: "bg-green-100 text-green-800",
      icon: CheckCircle,
    },
    open: {
      variant: "outline" as const,
      color: "bg-blue-100 text-blue-800",
      icon: Clock,
    },
    draft: {
      variant: "secondary" as const,
      color: "bg-gray-100 text-gray-800",
      icon: FileText,
    },
    void: {
      variant: "destructive" as const,
      color: "bg-red-100 text-red-800",
      icon: XCircle,
    },
    uncollectible: {
      variant: "destructive" as const,
      color: "bg-red-100 text-red-800",
      icon: XCircle,
    },
  };

  const config =
    statusConfig[status.toLowerCase() as keyof typeof statusConfig] || statusConfig.draft;
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="text-xs">
      <Icon className="h-3 w-3 mr-1" />
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
}

function handleDownload(downloadUrl: string | null) {
  if (!downloadUrl) {
    console.warn("No download URL available for invoice");
    return;
  }

  // Open the download URL in a new tab
  window.open(downloadUrl, "_blank");
}

export function InvoicesSection({ invoices }: InvoicesSectionProps) {
  if (invoices.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Invoice History
        </h3>

        <div className="text-center py-8">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Invoices</h4>
          <p className="text-gray-500 dark:text-gray-400">
            No invoices have been generated for this account yet.
          </p>
        </div>
      </div>
    );
  }

  // Sort invoices by creation date (newest first)
  const sortedInvoices = [...invoices].sort((a, b) => b.created - a.created);

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Invoice History
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {invoices.length} invoice{invoices.length !== 1 ? "s" : ""}
        </span>
      </div>

      <div className="space-y-3">
        {sortedInvoices.map((invoice) => (
          <div
            key={invoice.id}
            className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <FileText className="h-5 w-5 text-blue-600" />
              <div>
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {invoice.number || `Invoice ${invoice.id.slice(-8)}`}
                  </p>
                  {getStatusBadge(invoice.status)}
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Created: {formatDate(invoice.created)}
                  {invoice.paidAt && ` • Paid: ${formatDate(invoice.paidAt)}`}
                  {invoice.dueDate && !invoice.paidAt && ` • Due: ${formatDate(invoice.dueDate)}`}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(invoice.amountPaid || invoice.amountDue, invoice.currency)}
                </p>
                {invoice.amountDue !== invoice.amountPaid && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Due: {formatCurrency(invoice.amountDue, invoice.currency)}
                  </p>
                )}
              </div>

              {invoice.downloadUrl && (
                <button
                  onClick={() => handleDownload(invoice.downloadUrl)}
                  className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                  title="Download Invoice"
                >
                  <Download className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {invoices.length > 5 && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600 text-center">
          <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
            View All Invoices
          </button>
        </div>
      )}

      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Invoices are automatically generated for your subscriptions. You can download PDF copies
          at any time.
        </p>
      </div>
    </div>
  );
}
