import { Package, AlertCircle, CheckCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Subscription } from "@/types/billing";

interface SubscriptionsSectionProps {
  subscriptions: Subscription[];
}

function formatCurrency(amount: number | null, currency: string): string {
  if (amount === null) return "N/A";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase(),
  }).format(amount / 100); // Stripe amounts are in cents
}

function formatDate(timestamp: number | null | undefined): string {
  if (!timestamp || typeof timestamp !== "number") {
    return "Invalid Date";
  }

  const date = new Date(timestamp * 1000);

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return "Invalid Date";
  }

  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

function getStatusBadge(status: string) {
  const statusConfig = {
    active: {
      variant: "default" as const,
      color: "bg-green-100 text-green-800",
      icon: CheckCircle,
    },
    past_due: {
      variant: "destructive" as const,
      color: "bg-yellow-100 text-yellow-800",
      icon: AlertCircle,
    },
    canceled: {
      variant: "secondary" as const,
      color: "bg-gray-100 text-gray-800",
      icon: AlertCircle,
    },
    cancelled: {
      variant: "secondary" as const,
      color: "bg-gray-100 text-gray-800",
      icon: AlertCircle,
    },
    trialing: {
      variant: "outline" as const,
      color: "bg-blue-100 text-blue-800",
      icon: CheckCircle,
    },
    incomplete: {
      variant: "destructive" as const,
      color: "bg-red-100 text-red-800",
      icon: AlertCircle,
    },
    incomplete_expired: {
      variant: "destructive" as const,
      color: "bg-red-100 text-red-800",
      icon: AlertCircle,
    },
    unpaid: {
      variant: "destructive" as const,
      color: "bg-red-100 text-red-800",
      icon: AlertCircle,
    },
  };

  const config =
    statusConfig[status.toLowerCase() as keyof typeof statusConfig] || statusConfig.canceled;
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="text-xs">
      <Icon className="h-3 w-3 mr-1" />
      {status.charAt(0).toUpperCase() + status.slice(1).replace("_", " ")}
    </Badge>
  );
}

function formatInterval(interval: string | null): string {
  if (!interval) return "N/A";
  const intervalMap: Record<string, string> = {
    day: "Daily",
    week: "Weekly",
    month: "Monthly",
    year: "Yearly",
  };
  return intervalMap[interval] || interval;
}

export function SubscriptionsSection({ subscriptions }: SubscriptionsSectionProps) {
  if (subscriptions.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Package className="h-5 w-5 mr-2" />
          Subscriptions
        </h3>

        <div className="text-center py-8">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Active Subscriptions
          </h4>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            You don't have any active subscriptions at the moment.
          </p>
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Browse Plans
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <Package className="h-5 w-5 mr-2" />
          Subscriptions
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {subscriptions.length} subscription
          {subscriptions.length !== 1 ? "s" : ""}
        </span>
      </div>

      <div className="space-y-4">
        {subscriptions.map((subscription) => (
          <div
            key={subscription.id}
            className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <Package className="h-5 w-5 text-blue-600" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {subscription.plan?.productName || "Unknown Plan"}
                  </h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">ID: {subscription.id}</p>
                </div>
              </div>
              {getStatusBadge(subscription.status)}
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-gray-500 dark:text-gray-400 text-xs mb-1">Price</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {subscription.plan
                    ? formatCurrency(subscription.plan.amount, subscription.plan.currency)
                    : "N/A"}
                </p>
              </div>

              <div>
                <p className="text-gray-500 dark:text-gray-400 text-xs mb-1">Billing Cycle</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {formatInterval(subscription.plan?.interval || null)}
                </p>
              </div>

              <div>
                <p className="text-gray-500 dark:text-gray-400 text-xs mb-1">Current Period</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {subscription.currentPeriodStart && subscription.currentPeriodEnd
                    ? `${formatDate(subscription.currentPeriodStart)} - ${formatDate(subscription.currentPeriodEnd)}`
                    : "Date not available"}
                </p>
              </div>

              <div>
                <p className="text-gray-500 dark:text-gray-400 text-xs mb-1">Auto Renew</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {subscription.cancelAtPeriodEnd ? "No" : "Yes"}
                </p>
              </div>
            </div>

            {subscription.cancelAtPeriodEnd && (
              <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    This subscription will cancel at the end of the current period{" "}
                    {subscription.currentPeriodEnd
                      ? `(${formatDate(subscription.currentPeriodEnd)})`
                      : ""}
                    .
                  </p>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
