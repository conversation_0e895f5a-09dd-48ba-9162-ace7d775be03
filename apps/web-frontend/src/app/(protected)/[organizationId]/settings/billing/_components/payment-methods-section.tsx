import { Credit<PERSON>ard, Star, MoreH<PERSON>zon<PERSON> } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { PaymentMethod } from "@/types/billing";

interface PaymentMethodsSectionProps {
  paymentMethods: PaymentMethod[];
}

function getCardBrandIcon(brand: string) {
  const brandLower = brand.toLowerCase();

  // You can replace these with actual brand icons
  const brandColors = {
    visa: "text-blue-600",
    mastercard: "text-red-600",
    amex: "text-green-600",
    discover: "text-orange-600",
    default: "text-gray-600",
  };

  return (
    <div
      className={`flex items-center justify-center w-12 h-8 rounded border ${brandColors[brandLower as keyof typeof brandColors] || brandColors.default}`}
    >
      <CreditCard className="h-4 w-4" />
    </div>
  );
}

function formatCardBrand(brand: string): string {
  const brandMap: Record<string, string> = {
    visa: "Visa",
    mastercard: "Mastercard",
    amex: "American Express",
    discover: "Discover",
    diners: "Diners Club",
    jcb: "JCB",
    unionpay: "UnionPay",
  };

  return brandMap[brand.toLowerCase()] || brand.charAt(0).toUpperCase() + brand.slice(1);
}

function formatExpiryDate(expMonth: number, expYear: number): string {
  return `${expMonth.toString().padStart(2, "0")}/${expYear.toString().slice(-2)}`;
}

export function PaymentMethodsSection({ paymentMethods }: PaymentMethodsSectionProps) {
  if (paymentMethods.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <CreditCard className="h-5 w-5 mr-2" />
          Payment Methods
        </h3>

        <div className="text-center py-8">
          <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Payment Methods
          </h4>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            No payment methods have been added to this account yet.
          </p>
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Add Payment Method
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <CreditCard className="h-5 w-5 mr-2" />
          Payment Methods
        </h3>
      </div>

      <div className="space-y-4">
        {(() => {
          // Create a more robust deduplication logic
          const seenCards = new Set();
          const uniqueMethods: PaymentMethod[] = [];

          // Process payment methods to keep only truly unique ones
          paymentMethods.forEach((method) => {
            if (method.card) {
              // Create a comprehensive unique key that includes all relevant card details
              const cardKey = `${method.card.brand.toLowerCase()}-${method.card.last4}-${method.card.expMonth}-${method.card.expYear}`;

              // Only add if we haven't seen this exact card before
              if (!seenCards.has(cardKey)) {
                seenCards.add(cardKey);
                uniqueMethods.push(method);
              }
            } else {
              // For non-card payment methods, add them directly
              uniqueMethods.push(method);
            }
          });

          return uniqueMethods.map((method) => (
            <div
              key={method.id}
              className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {method.card && getCardBrandIcon(method.card.brand)}

                <div>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {method.card ? formatCardBrand(method.card.brand) : method.type}
                    </p>
                    {method.isDefault && (
                      <Badge variant="secondary" className="text-xs">
                        <Star className="h-3 w-3 mr-1" />
                        Default
                      </Badge>
                    )}
                  </div>
                  {method.card && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      •••• •••• •••• {method.card.last4} • Expires{" "}
                      {formatExpiryDate(method.card.expMonth, method.card.expYear)}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Badge variant={method.isDefault ? "default" : "outline"} className="text-xs">
                  {method.isDefault ? "Primary" : "Secondary"}
                </Badge>

                <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600">
                  <MoreHorizontal className="h-4 w-4" />
                </button>
              </div>
            </div>
          ));
        })()}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Payment methods are securely stored and encrypted. You can add, remove, or set a default
          payment method at any time.
        </p>
      </div>
    </div>
  );
}
