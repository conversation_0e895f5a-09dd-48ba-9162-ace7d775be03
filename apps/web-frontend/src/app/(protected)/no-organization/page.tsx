"use client";

import { useState } from "react";
import { useSession } from "@/providers/session-provider";
import { useCreateOrganization } from "@/hooks/use-create-organization";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CreateOrganizationDialog } from "@/components/create-organization-dialog";
import { Building2, Users, Plus, ArrowRight, Shield, MessageSquare, BookOpen } from "lucide-react";

export default function NoOrganizationPage() {
  const { session } = useSession();
  const { createOrganization, isLoading } = useCreateOrganization();
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const handleCreateOrganization = () => {
    setShowCreateDialog(true);
  };

  const handleCreateOrganizationSubmit = async (organizationName: string) => {
    try {
      const result = await createOrganization({
        companyName: organizationName,
      });
      if (result) {
        setShowCreateDialog(false);
      }
    } catch (error) {
      console.error("Failed to create organization:", error);
    }
  };

  const handleJoinOrganization = async () => {
    try {
      // TODO: Implement join organization flow
      // For now, redirect to a placeholder
      console.log("Join organization functionality not implemented yet");
    } catch (error) {
      console.error("Failed to join organization:", error);
    }
  };

  const features = [
    {
      icon: Shield,
      title: "Security & Compliance",
      description: "Get expert guidance on security frameworks and compliance requirements",
    },
    {
      icon: MessageSquare,
      title: "AI-Powered Chat",
      description: "Ask security questions and get instant, accurate responses",
    },
    {
      icon: BookOpen,
      title: "Learning Management",
      description: "Access training materials and track your team's progress",
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <div className="flex justify-center mb-6">
          <div className="p-4 bg-primary/10 rounded-full">
            <Building2 className="h-12 w-12 text-primary" />
          </div>
        </div>

        <h1 className="text-4xl font-bold mb-4">Welcome to AskInfosec</h1>

        <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
          You're not currently a member of any organization. Join an existing organization or create
          a new one to get started with our security and compliance platform.
        </p>

        {session?.user && (
          <p className="text-sm text-muted-foreground">
            Signed in as{" "}
            <span className="font-medium">{session.user.name || session.user.email}</span>
          </p>
        )}
      </div>

      {/* Action Cards */}
      <div className="grid md:grid-cols-2 gap-6 mb-12">
        <Card className="border-2 border-dashed border-primary/20 hover:border-primary/40 transition-colors">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <Plus className="h-6 w-6 text-primary" />
              </div>
            </div>
            <CardTitle className="text-xl">Create Organization</CardTitle>
            <CardDescription>
              Start a new organization and invite team members to collaborate on security and
              compliance.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={handleCreateOrganization} className="w-full" size="lg">
              Create Organization
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>

        <Card className="border-2 border-dashed border-secondary/20 hover:border-secondary/40 transition-colors">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-secondary/10 rounded-full">
                <Users className="h-6 w-6 text-secondary-foreground" />
              </div>
            </div>
            <CardTitle className="text-xl">Join Organization</CardTitle>
            <CardDescription>
              Join an existing organization using an invitation link or organization code.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button
              onClick={handleJoinOrganization}
              variant="secondary"
              className="w-full"
              size="lg"
            >
              Join Organization
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Features Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-center mb-8">What you can do with AskInfosec</h2>

        <div className="grid md:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-muted rounded-full">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Help Section */}
      <Card className="bg-muted/50">
        <CardHeader className="text-center">
          <CardTitle className="text-lg">Need Help?</CardTitle>
          <CardDescription>
            If you're having trouble joining or creating an organization, contact your administrator
            or reach out to our support team.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" size="sm">
              Contact Support
            </Button>
            <Button variant="outline" size="sm">
              View Documentation
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Create Organization Dialog */}
      <CreateOrganizationDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleCreateOrganizationSubmit}
        isLoading={isLoading}
      />
    </div>
  );
}
