"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function CssDebugger() {
  const [cssVariables, setCssVariables] = useState<Record<string, string>>({});
  const [cssLoaded, setCssLoaded] = useState(false);
  const [shadcnVariablesFound, setShadcnVariablesFound] = useState(0);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [utilityClassesWorking, setUtilityClassesWorking] = useState(false);

  useEffect(() => {
    // Only run on client side
    if (typeof window === "undefined" || typeof document === "undefined") return;

    const updateCssVariables = () => {
      try {
        const computedStyle = window.getComputedStyle(document.documentElement);
        const variables: Record<string, string> = {};

        // Get all CSS custom properties
        for (let i = 0; i < computedStyle.length; i++) {
          const property = computedStyle[i];
          if (property && property.startsWith("--")) {
            variables[property] = computedStyle.getPropertyValue(property);
          }
        }

        setCssVariables(variables);

        // Check if shadcn variables are loaded
        const shadcnVars = [
          "--background",
          "--foreground",
          "--card",
          "--card-foreground",
          "--popover",
          "--popover-foreground",
          "--primary",
          "--primary-foreground",
          "--secondary",
          "--secondary-foreground",
          "--muted",
          "--muted-foreground",
          "--accent",
          "--accent-foreground",
          "--destructive",
          "--destructive-foreground",
          "--border",
          "--input",
          "--ring",
          "--radius",
        ];

        const found = shadcnVars.filter((varName) => variables[varName]);
        setShadcnVariablesFound(found.length);
        setCssLoaded(found.length > 0);

        // Check if dark mode is active
        setIsDarkMode(document.documentElement.classList.contains("dark"));

        // Check if utility classes are working
        const testElement = document.createElement("div");
        testElement.className = "bg-primary text-primary-foreground rounded-sm";
        testElement.style.position = "absolute";
        testElement.style.left = "-9999px";
        testElement.style.top = "-9999px";
        document.body.appendChild(testElement);

        const computedTestStyle = window.getComputedStyle(testElement);
        const hasBackground =
          computedTestStyle.backgroundColor !== "rgba(0, 0, 0, 0)" &&
          computedTestStyle.backgroundColor !== "transparent";
        const hasColor =
          computedTestStyle.color !== "rgba(0, 0, 0, 0)" &&
          computedTestStyle.color !== "transparent";
        const hasBorderRadius = computedTestStyle.borderRadius !== "0px";

        setUtilityClassesWorking(hasBackground && hasColor && hasBorderRadius);

        document.body.removeChild(testElement);
      } catch (error) {
        console.error("Error updating CSS variables:", error);
      }
    };

    updateCssVariables();

    // Update on theme changes - only if MutationObserver is available
    if (typeof window.MutationObserver !== "undefined") {
      const observer = new window.MutationObserver(updateCssVariables);
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ["class"],
      });

      return () => observer.disconnect();
    }
  }, []);

  const expectedLightVars = {
    "--background": "0 0% 100%",
    "--foreground": "240 10% 3.9%",
    "--primary": "16 100% 50%",
    "--secondary": "240 4.8% 95.9%",
    "--muted": "240 4.8% 95.9%",
  };

  const expectedDarkVars = {
    "--background": "240 10% 3.9%",
    "--foreground": "0 0% 98%",
    "--primary": "16 100% 50%",
    "--secondary": "240 3.7% 15.9%",
    "--muted": "240 3.7% 15.9%",
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>CSS Variables Status</CardTitle>
        <CardDescription>Check if shadcn-ui CSS variables are loaded</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <span
              className={`w-4 h-4 rounded-full ${cssLoaded ? "bg-green-500" : "bg-red-500"}`}
            ></span>
            <span>ShadCN Variables Loaded: {cssLoaded ? "Yes" : "No"}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="w-4 h-4 rounded-full bg-blue-500"></span>
            <span>ShadCN Variables Found: {shadcnVariablesFound} / 10</span>
          </div>
          <div className="flex items-center gap-2">
            <span
              className={`w-4 h-4 rounded-full ${utilityClassesWorking ? "bg-green-500" : "bg-red-500"}`}
            ></span>
            <span>Utility Classes Working: {utilityClassesWorking ? "Yes" : "No"}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="w-4 h-4 rounded-full bg-purple-500"></span>
            <span>Current Theme: {isDarkMode ? "Dark Mode" : "Light Mode"}</span>
          </div>
        </div>

        <div className="text-sm">
          <strong>Primary Variable:</strong> {cssVariables["--primary"] || "Not found"}
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Expected Values ({isDarkMode ? "Dark" : "Light"} Mode):</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {Object.entries(isDarkMode ? expectedDarkVars : expectedLightVars).map(
              ([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="font-mono">{key}:</span>
                  <span className="font-mono text-muted-foreground">{value}</span>
                </div>
              ),
            )}
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Browser CSS Variables (Live):</h4>
          <div className="max-h-40 overflow-y-auto text-xs space-y-1">
            {Object.entries(cssVariables)
              .filter(([key]) => key.startsWith("--"))
              .slice(0, 10)
              .map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="font-mono">{key}:</span>
                  <span className="font-mono text-muted-foreground">{value}</span>
                </div>
              ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
