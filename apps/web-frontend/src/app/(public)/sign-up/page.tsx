"use client";

import { useState, useEffect } from "react";
import { sendVerificationOtp } from "@/lib/auth-client";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { AuthLayout } from "@/components/auth-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useSession } from "@/providers/session-provider";

export default function SignUpPage() {
  const [formData, setFormData] = useState({
    email: "",
    firstName: "",
    lastName: "",
    companyName: "",
    acceptTerms: false,
  });
  const [status, setStatus] = useState<"idle" | "loading" | "sent" | "error">("idle");
  const [error, setError] = useState<string>("");
  const router = useRouter();
  const { session, isLoading: sessionLoading } = useSession();

  // Check if user is already logged in and redirect appropriately
  useEffect(() => {
    if (!sessionLoading && session) {
      // User is logged in, redirect to no-organization page
      // They can create/join an organization from there
      router.push("/no-organization");
    }
  }, [session, sessionLoading, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus("loading");
    setError("");

    try {
      await sendVerificationOtp({
        email: formData.email,
        type: "email-verification",
      });
      setStatus("sent");
      // Redirect to verify page after sending OTP
      setTimeout(() => {
        router.push(`/verify?email=${encodeURIComponent(formData.email)}`);
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to send OTP");
      setStatus("error");
    }
  };

  // Show loading while checking authentication status
  if (sessionLoading) {
    return (
      <AuthLayout
        title="Create your account"
        subtitle="Join AskInfosec to secure your organization"
      >
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <div className="text-muted-foreground">Loading...</div>
        </div>
      </AuthLayout>
    );
  }

  // Don't render the form if user is already logged in
  if (session) {
    return null;
  }

  return (
    <AuthLayout title="Create your account" subtitle="Join AskInfosec to secure your organization">
      <form onSubmit={onSubmit} className="space-y-6">
        {error && (
          <Card className="border-destructive/20 bg-destructive/10">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{error}</p>
            </CardContent>
          </Card>
        )}

        {status === "sent" && (
          <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
            <CardContent className="p-4">
              <p className="text-green-700 dark:text-green-300 text-sm">
                Account created successfully! Redirecting to verification page...
              </p>
            </CardContent>
          </Card>
        )}

        {/* Email Input - Required */}
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium text-foreground">
            Email address <span className="text-destructive">*</span>
          </label>
          <input
            id="email"
            name="email"
            type="email"
            required
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Enter your email"
            className="w-full px-4 py-3 border border-input rounded-lg focus:ring-2 focus:ring-ring focus:border-ring transition-colors bg-background text-foreground placeholder-muted-foreground"
          />
        </div>

        {/* Name Fields - Optional */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="firstName" className="text-sm font-medium text-foreground">
              First name <span className="text-muted-foreground text-xs">(optional)</span>
            </label>
            <input
              id="firstName"
              name="firstName"
              type="text"
              value={formData.firstName}
              onChange={handleInputChange}
              placeholder="Enter first name"
              className="w-full px-4 py-3 border border-input rounded-lg focus:ring-2 focus:ring-ring focus:border-ring transition-colors bg-background text-foreground placeholder-muted-foreground"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="lastName" className="text-sm font-medium text-foreground">
              Last name <span className="text-muted-foreground text-xs">(optional)</span>
            </label>
            <input
              id="lastName"
              name="lastName"
              type="text"
              value={formData.lastName}
              onChange={handleInputChange}
              placeholder="Enter last name"
              className="w-full px-4 py-3 border border-input rounded-lg focus:ring-2 focus:ring-ring focus:border-ring transition-colors bg-background text-foreground placeholder-muted-foreground"
            />
          </div>
        </div>

        {/* Company Name - Optional */}
        <div className="space-y-2">
          <label htmlFor="companyName" className="text-sm font-medium text-foreground">
            Company name <span className="text-muted-foreground text-xs">(optional)</span>
          </label>
          <input
            id="companyName"
            name="companyName"
            type="text"
            value={formData.companyName}
            onChange={handleInputChange}
            placeholder="Enter company name"
            className="w-full px-4 py-3 border border-input rounded-lg focus:ring-2 focus:ring-ring focus:border-ring transition-colors bg-background text-foreground placeholder-muted-foreground"
          />
        </div>

        {/* Terms Checkbox */}
        <div className="flex items-start space-x-3">
          <input
            id="acceptTerms"
            name="acceptTerms"
            type="checkbox"
            required
            checked={formData.acceptTerms}
            onChange={handleInputChange}
            className="mt-1 h-4 w-4 text-primary focus:ring-ring border-input rounded bg-background"
          />
          <label htmlFor="acceptTerms" className="text-sm text-muted-foreground">
            I agree to the{" "}
            <Link href="/terms" className="font-medium text-primary hover:underline">
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link href="/privacy" className="font-medium text-primary hover:underline">
              Privacy Policy
            </Link>
          </label>
        </div>

        {/* Create Account Button */}
        <Button
          type="submit"
          disabled={status === "loading" || !formData.acceptTerms}
          className="w-full"
        >
          {status === "loading" ? "Creating account..." : "Create account"}
        </Button>

        {/* Sign In Link */}
        <div className="pt-2">
          <p className="text-center text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link href="/sign-in" className="font-semibold text-primary hover:underline">
              Sign in
            </Link>
          </p>
        </div>
      </form>
    </AuthLayout>
  );
}
