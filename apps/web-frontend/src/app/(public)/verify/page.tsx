"use client";

import { useState, useEffect, Suspense } from "react";
import { signInWithEmailOtp, sendVerificationOtp } from "@/lib/auth-client";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { AuthLayout } from "@/components/auth-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

function VerifyPageContent() {
  const [otp, setOtp] = useState("");
  const [status, setStatus] = useState<"idle" | "loading" | "success" | "error">("idle");
  const [error, setError] = useState<string>("");
  const [timeLeft, setTimeLeft] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  useEffect(() => {
    if (!email) {
      router.push("/sign-in");
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [email, router]);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setStatus("loading");
    setError("");
    try {
      await signInWithEmailOtp({ email, otp, rememberMe: true });
      setStatus("success");

      // Check if we have organization parameters from invite acceptance
      const orgId = searchParams.get("org");
      const message = searchParams.get("message");

      // Redirect appropriately based on context
      setTimeout(() => {
        if (orgId && message === "invite-accepted") {
          // User came from invite acceptance, redirect to their organization
          router.push(`/${orgId}`);
        } else {
          // Let the root page handle the redirect
          router.push("/");
        }
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Invalid verification code");
      setStatus("error");
    }
  };

  const handleResend = async () => {
    if (!email || !canResend) return;

    try {
      await sendVerificationOtp({ email, type: "sign-in" });
      setTimeLeft(30);
      setCanResend(false);
    } catch {
      setError("Failed to resend code. Please try again.");
    }
  };

  if (!email) {
    return null;
  }

  return (
    <AuthLayout
      title="Verify your email"
      subtitle={`We've sent a verification code to ${email}`}
      showBackButton
      onBack={() => router.push("/sign-in")}
    >
      <form onSubmit={onSubmit} className="space-y-8">
        {error && (
          <Card className="border-destructive/20 bg-destructive/10">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{error}</p>
            </CardContent>
          </Card>
        )}

        {status === "success" && (
          <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
            <CardContent className="p-4">
              <p className="text-green-700 dark:text-green-300 text-sm">
                Verification successful! Redirecting...
              </p>
            </CardContent>
          </Card>
        )}

        {/* OTP Input */}
        <div className="space-y-2">
          <label
            htmlFor="otp"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Verification Code
          </label>
          <input
            id="otp"
            type="text"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            placeholder="Enter 6-digit code"
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            maxLength={6}
            required
          />
        </div>

        {/* Submit Button */}
        <Button type="submit" className="w-full" disabled={status === "loading" || !otp}>
          {status === "loading" ? "Verifying..." : "Verify Email"}
        </Button>

        {/* Resend Code */}
        <div className="text-center space-y-2">
          {!canResend ? (
            <p className="text-sm text-muted-foreground">Resend code in {timeLeft} seconds</p>
          ) : (
            <Button type="button" variant="outline" onClick={handleResend} className="w-full">
              Resend Code
            </Button>
          )}
        </div>

        {/* Back to Sign In */}
        <div className="text-center">
          <Link
            href="/sign-in"
            className="text-sm text-muted-foreground hover:text-foreground underline"
          >
            Back to Sign In
          </Link>
        </div>
      </form>
    </AuthLayout>
  );
}

export default function VerifyPage() {
  return (
    <Suspense
      fallback={
        <AuthLayout title="Verify your email" subtitle="Loading verification page...">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <div className="text-muted-foreground">Loading...</div>
          </div>
        </AuthLayout>
      }
    >
      <VerifyPageContent />
    </Suspense>
  );
}
