"use client";

import { useState, useEffect, Suspense, useCallback, useMemo } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { AuthLayout } from "@/components/auth-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  CheckCircle,
  XCircle,
  Clock,
  Mail,
  Shield,
  Users,
  Building,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import { validateInviteServer, acceptInviteServer } from "./_actions/accept-invite";
import { toast } from "sonner";
import { ErrorBoundary } from "react-error-boundary";

type InviteState = "loading" | "valid" | "invalid" | "expired" | "accepting" | "accepted" | "error";

interface InviteData {
  email: string;
  organizationId: string;
  organizationName?: string;
  roleName: string;
  roleDescription?: string;
  expiresAt: string;
  message?: string;
}

// Error fallback component
function ErrorFallback({
  error: _error,
  resetErrorBoundary,
}: {
  error: Error;
  resetErrorBoundary: () => void;
}) {
  return (
    <AuthLayout title="Something went wrong" subtitle="An error occurred">
      <div className="text-center space-y-4">
        <AlertTriangle className="h-16 w-16 text-destructive mx-auto" />
        <div>
          <h2 className="text-xl font-semibold text-destructive mb-2">Unexpected Error</h2>
          <p className="text-muted-foreground">
            Something went wrong while processing your invitation.
          </p>
        </div>
        <div className="flex gap-2 justify-center">
          <Button onClick={resetErrorBoundary} variant="outline">
            Try Again
          </Button>
          <Button variant="outline" onClick={() => (window.location.href = "/sign-in")}>
            Go to Sign In
          </Button>
        </div>
      </div>
    </AuthLayout>
  );
}

// Loading fallback component
function LoadingFallback() {
  return (
    <AuthLayout title="Join Organization" subtitle="Accept your invitation to get started">
      <div className="flex items-center justify-center p-8">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <div className="text-muted-foreground">Loading...</div>
        </div>
      </div>
    </AuthLayout>
  );
}

function AcceptInvitePageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [state, setState] = useState<InviteState>("loading");
  const [inviteData, setInviteData] = useState<InviteData | null>(null);
  const [error, setError] = useState<string>("");
  // Check for URL parameters
  const urlParams = useMemo(
    () => ({
      token: searchParams.get("token"),
      orgId: searchParams.get("org"),
      error: searchParams.get("error"),
    }),
    [searchParams],
  );

  // Memoize validation function
  const validateInvite = useCallback(async (token: string, orgId: string) => {
    try {
      const result = await validateInviteServer(orgId, { token });

      if (!result.valid) {
        if (result.error?.includes("expired")) {
          setState("expired");
          setError("This invitation has expired.");
        } else {
          setState("invalid");
          setError(result.error || "Invalid invitation.");
        }
        return;
      }

      // Set invite data
      if (result.invite && result.role) {
        setInviteData({
          email: result.invite.email,
          organizationId: result.invite.organizationId,
          organizationName: result.organization?.name || "Organization",
          roleName: result.role.name,
          roleDescription: result.role.description || "",
          expiresAt: result.invite.expiresAt,
          message: result.invite.message,
        });
        setState("valid");
      } else {
        setState("invalid");
        setError("Invalid invitation data.");
      }
    } catch (err) {
      setState("error");
      setError("Failed to validate invitation. Please try again.");
      console.error("Validation error:", err);
    }
  }, []);

  // Initialize token and validate
  useEffect(() => {
    const initializeAndValidate = async () => {
      // Check for URL error parameters first
      if (urlParams.error) {
        setState("invalid");
        switch (urlParams.error) {
          case "invalid-link":
            setError("Invalid invitation link.");
            break;
          case "invalid-token":
            setError("Invalid invitation token.");
            break;
          case "server-error":
            setError("Server error occurred. Please try again.");
            break;
          default:
            setError("An error occurred processing your invitation.");
        }
        return;
      }

      // Get token from URL parameters
      const token = urlParams.token;
      const orgId = urlParams.orgId;

      if (!token || !orgId) {
        setState("invalid");
        setError("Invalid invitation link. Missing token or organization ID.");
        return;
      }

      await validateInvite(token, orgId);
    };

    initializeAndValidate();
  }, [urlParams, validateInvite]);

  const handleAcceptInvite = useCallback(async () => {
    const currentToken = urlParams.token;
    const currentOrgId = urlParams.orgId;

    if (!currentToken || !currentOrgId) return;

    setState("accepting");
    setError("");

    try {
      // Add timeout to prevent hanging requests
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Request timeout")), 30000),
      );

      const acceptPromise = acceptInviteServer(currentOrgId, {
        token: currentToken,
      });

      const result = (await Promise.race([acceptPromise, timeoutPromise])) as Awaited<
        ReturnType<typeof acceptInviteServer>
      >;

      if (result.success) {
        setState("accepted");
        toast.success("Invitation accepted successfully!");

        // Redirect to sign-in page after a short delay
        setTimeout(() => {
          router.push(`/sign-in?message=invite-accepted&org=${currentOrgId}`);
        }, 2000);
      } else {
        throw new Error(result.error || "Failed to accept invitation");
      }
    } catch (err) {
      setState("valid"); // Return to valid state
      const errorMessage = err instanceof Error ? err.message : "Failed to accept invitation";
      setError(errorMessage);
      toast.error(errorMessage);
      console.error("Accept error:", err);
    }
  }, [urlParams.token, urlParams.orgId, router]);

  const getRoleIcon = (roleName: string) => {
    switch (roleName.toLowerCase()) {
      case "admin":
        return Shield;
      case "user":
      case "member":
        return Users;
      case "vendor":
        return Building;
      default:
        return Users;
    }
  };

  const renderContent = () => {
    switch (state) {
      case "loading":
        return (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <div className="text-muted-foreground">Validating invitation...</div>
          </div>
        );

      case "invalid":
        return (
          <div className="text-center space-y-4">
            <XCircle className="h-16 w-16 text-destructive mx-auto" />
            <div>
              <h2 className="text-xl font-semibold text-destructive mb-2">Invalid Invitation</h2>
              <p className="text-muted-foreground">{error}</p>
            </div>
            <Button variant="outline" onClick={() => router.push("/sign-in")} className="mt-4">
              Go to Sign In
            </Button>
          </div>
        );

      case "expired":
        return (
          <div className="text-center space-y-4">
            <Clock className="h-16 w-16 text-orange-500 mx-auto" />
            <div>
              <h2 className="text-xl font-semibold text-orange-600 mb-2">Invitation Expired</h2>
              <p className="text-muted-foreground">{error}</p>
              <p className="text-sm text-muted-foreground mt-2">
                Please contact your organization administrator for a new invitation.
              </p>
            </div>
            <Button variant="outline" onClick={() => router.push("/sign-in")} className="mt-4">
              Go to Sign In
            </Button>
          </div>
        );

      case "error":
        return (
          <div className="text-center space-y-4">
            <AlertTriangle className="h-16 w-16 text-orange-500 mx-auto" />
            <div>
              <h2 className="text-xl font-semibold text-orange-600 mb-2">Something went wrong</h2>
              <p className="text-muted-foreground">{error}</p>
            </div>
            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={() => window.location.reload()}>
                Try Again
              </Button>
              <Button variant="outline" onClick={() => router.push("/sign-in")}>
                Go to Sign In
              </Button>
            </div>
          </div>
        );

      case "accepted":
        return (
          <div className="text-center space-y-4">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
            <div>
              <h2 className="text-xl font-semibold text-green-600 mb-2">Invitation Accepted!</h2>
              <p className="text-muted-foreground">
                Welcome to {inviteData?.organizationName}! Redirecting you to sign in...
              </p>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Redirecting...
            </div>
          </div>
        );

      case "accepting":
        return (
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
            <div>
              <h2 className="text-xl font-semibold mb-2">Accepting Invitation...</h2>
              <p className="text-muted-foreground">Please wait while we process your invitation.</p>
            </div>
          </div>
        );

      case "valid":
      default: {
        if (!inviteData) return null;

        const RoleIcon = getRoleIcon(inviteData.roleName);

        return (
          <div className="space-y-6">
            {/* Invitation Details */}
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                  <Mail className="h-8 w-8 text-primary" />
                </div>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-2">
                  You're invited to join {inviteData.organizationName}
                </h2>
                <p className="text-muted-foreground">
                  You've been invited to join as a <strong>{inviteData.roleName}</strong>
                </p>
              </div>
            </div>

            {/* Invitation Info Card */}
            <Card className="border-primary/20 bg-primary/5">
              <CardContent className="p-4 space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    <span className="text-muted-foreground">Email:</span>{" "}
                    <span className="font-medium">{inviteData.email}</span>
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <RoleIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    <span className="text-muted-foreground">Role:</span>{" "}
                    <span className="font-medium">{inviteData.roleName}</span>
                    {inviteData.roleDescription && (
                      <span className="text-muted-foreground ml-1">
                        - {inviteData.roleDescription}
                      </span>
                    )}
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    <span className="text-muted-foreground">Expires:</span>{" "}
                    <span className="font-medium">
                      {new Date(inviteData.expiresAt).toLocaleDateString()}
                    </span>
                  </span>
                </div>

                {inviteData.message && (
                  <div className="pt-2 border-t border-primary/20">
                    <p className="text-sm text-muted-foreground italic">"{inviteData.message}"</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Error Display */}
            {error && (
              <Card className="border-destructive/20 bg-destructive/10">
                <CardContent className="p-4">
                  <p className="text-destructive text-sm">{error}</p>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button onClick={handleAcceptInvite} className="flex-1" disabled={false}>
                Accept Invitation
              </Button>
              <Button variant="outline" onClick={() => router.push("/sign-in")} disabled={false}>
                Decline
              </Button>
            </div>

            {/* Help Text */}
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                By accepting this invitation, you'll be able to access {inviteData.organizationName}{" "}
                with {inviteData.roleName.toLowerCase()} permissions.
              </p>
            </div>
          </div>
        );
      }
    }
  };

  return (
    <AuthLayout title="Join Organization" subtitle="Accept your invitation to get started">
      {renderContent()}
    </AuthLayout>
  );
}

export default function AcceptInvitePage() {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<LoadingFallback />}>
        <AcceptInvitePageContent />
      </Suspense>
    </ErrorBoundary>
  );
}
