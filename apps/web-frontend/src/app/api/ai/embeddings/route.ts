import { NextResponse, NextRequest } from "next/server";

export const dynamic = "force-dynamic";

interface EmbeddingsRequest {
  documents: Array<{
    id: string;
    name: string;
    content: string;
  }>;
  options: {
    maxTokens: number;
    overlapTokens: number;
    clearExisting: boolean;
  };
}

interface EmbeddingsResponse {
  success: boolean;
  message?: string;
  error?: string;
  processedDocuments?: number;
}

export async function POST(request: NextRequest) {
  try {
    const { documents, options, organizationId } = (await request.json()) as {
      documents: EmbeddingsRequest["documents"];
      options: EmbeddingsRequest["options"];
      organizationId: string;
    };

    if (!documents || !Array.isArray(documents) || documents.length === 0) {
      return NextResponse.json(
        { success: false, error: "Missing or empty documents array" },
        { status: 400 },
      );
    }

    if (!organizationId) {
      return NextResponse.json(
        { success: false, error: "Missing organizationId" },
        { status: 400 },
      );
    }

    // Validate documents
    for (const doc of documents) {
      if (!doc.id || !doc.name || !doc.content) {
        return NextResponse.json(
          { success: false, error: "Each document must have id, name, and content" },
          { status: 400 },
        );
      }
    }

    const baseUrl = process.env.ANTER_API_URL;
    const internalSecret = process.env.INTERNAL_SECRET;
    const timeout = parseInt(process.env.ANTER_API_TIMEOUT || "60000", 10); // Longer timeout for embeddings

    if (!baseUrl || !internalSecret) {
      return NextResponse.json(
        {
          success: false,
          error: "Server configuration missing ANTER_API_URL or INTERNAL_SECRET",
        },
        { status: 500 },
      );
    }

    // Prepare the request for the external AI API
    const embeddingsRequest: EmbeddingsRequest = {
      documents,
      options: {
        maxTokens: options?.maxTokens || 2000,
        overlapTokens: options?.overlapTokens || 200,
        clearExisting: options?.clearExisting ?? true,
      },
    };

    // Generate correlation IDs for tracking
    const requestId = `emb_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const timestamp = new Date().toISOString();

    // Call the external AI API
    const url = new URL("/v1/external/embeddings/submit", baseUrl);

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "X-Internal-Secret": internalSecret,
      "X-Request-ID": requestId,
      "X-Organization-ID": organizationId,
      "X-Timestamp": timestamp,
    };

    console.log("🔍 Calling external embeddings API:", {
      url: url.toString(),
      documentsCount: documents.length,
      organizationId,
      requestId,
    });

    const response = await fetch(url.toString(), {
      method: "POST",
      headers,
      body: JSON.stringify(embeddingsRequest),
      signal: AbortSignal.timeout(timeout),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "");
      console.error("External embeddings API error:", {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });

      return NextResponse.json(
        {
          success: false,
          error: `External API error: ${response.status} ${response.statusText}${errorText ? ` - ${errorText}` : ""}`,
        },
        { status: response.status },
      );
    }

    const result = await response.json();

    console.log("✅ External embeddings API success:", {
      requestId,
      result,
    });

    // Return success response
    const successResponse: EmbeddingsResponse = {
      success: true,
      message: `Successfully generated embeddings for ${documents.length} document(s)`,
      processedDocuments: documents.length,
    };

    return NextResponse.json(successResponse);
  } catch (error) {
    console.error("Embeddings API error:", error);

    let errorMessage = "An unexpected error occurred while generating embeddings";

    if (error instanceof Error) {
      if (error.name === "TimeoutError" || error.message.includes("timeout")) {
        errorMessage =
          "Request timed out. The embeddings generation may take longer than expected.";
      } else if (error.message.includes("network") || error.message.includes("fetch")) {
        errorMessage = "Network error occurred while connecting to the AI service.";
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 },
    );
  }
}
