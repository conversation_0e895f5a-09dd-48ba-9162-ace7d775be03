#!/bin/bash

echo "Fixing all @askinfosec/shadcn-ui imports..."

# Find all files with @askinfosec/shadcn-ui imports
files=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "@askinfosec/shadcn-ui")

echo "Found $(echo "$files" | wc -l) files to fix"

# Process each file
for file in $files; do
  echo "Processing $file"
  
  # Replace @askinfosec/shadcn-ui/lib/utils with @/lib/utils
  sed -i '' 's|@askinfosec/shadcn-ui/lib/utils|@/lib/utils|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/config/data-table with @/config/data-table
  sed -i '' 's|@askinfosec/shadcn-ui/config/data-table|@/config/data-table|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/lib/parsers with @/lib/parsers
  sed -i '' 's|@askinfosec/shadcn-ui/lib/parsers|@/lib/parsers|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/lib/data-table with @/lib/data-table
  sed -i '' 's|@askinfosec/shadcn-ui/lib/data-table|@/lib/data-table|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/lib/format with @/lib/format
  sed -i '' 's|@askinfosec/shadcn-ui/lib/format|@/lib/format|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/hooks/use-callback-ref with @/hooks/use-callback-ref
  sed -i '' 's|@askinfosec/shadcn-ui/hooks/use-callback-ref|@/hooks/use-callback-ref|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/hooks/use-debounced-callback with @/hooks/use-debounced-callback
  sed -i '' 's|@askinfosec/shadcn-ui/hooks/use-debounced-callback|@/hooks/use-debounced-callback|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/hooks/use-data-table with @/hooks/use-data-table
  sed -i '' 's|@askinfosec/shadcn-ui/hooks/use-data-table|@/hooks/use-data-table|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/types/data-table with @/types/data-table
  sed -i '' 's|@askinfosec/shadcn-ui/types/data-table|@/types/data-table|g' "$file"
  
  # Replace @askinfosec/shadcn-ui/config/data-table with @/config/data-table
  sed -i '' 's|@askinfosec/shadcn-ui/config/data-table|@/config/data-table|g' "$file"
done

echo "Done fixing imports!"
