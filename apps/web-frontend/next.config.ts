import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Allow importing from packages outside this app directory
  experimental: {
    externalDir: true,
    serverActions: {
      bodySizeLimit: "10mb",
    },
  },
  // Ensure workspace packages are transpiled for Next.js
  transpilePackages: ["@askinfosec/shared", "@askinfosec/types"],
  i18n: {
    // Enable i18n with supported locales
    locales: ["en", "es"],
    defaultLocale: "en",
    // Let Next.js detect locale by default; can be toggled later if needed
    localeDetection: false,
  },
};

export default nextConfig;
