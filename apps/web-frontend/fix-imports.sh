#!/bin/bash

# Script to replace @askinfosec/shadcn-ui imports with local imports

# Find all files with @askinfosec/shadcn-ui imports
files=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "@askinfosec/shadcn-ui")

echo "Found $(echo "$files" | wc -l) files to fix"

# Process each file
for file in $files; do
  echo "Processing $file"
  
  # Replace common imports
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/button"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/input"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/label"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/card"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/badge"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/dialog"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/table"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/select"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/checkbox"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/textarea"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/separator"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/skeleton"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/slider"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/tabs"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/toggle"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/tooltip"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/form"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/dropdown-menu"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/popover"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/accordion"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/avatar"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/breadcrumb"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/calendar"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/carousel"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/chart"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/command"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/drawer"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/sheet"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/sonner"|g' "$file"
  sed -i '' 's|from "@askinfosec/shadcn-ui"|from "@/components/ui/toggle-group"|g' "$file"
  
  # Handle specific component imports that need to be grouped
  sed -i '' 's|{ Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/button"|{ Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"|g' "$file"
  sed -i '' 's|{ Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from "@/components/ui/button"|{ Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from "@/components/ui/dialog"|g' "$file"
  sed -i '' 's|{ Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/button"|{ Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"|g' "$file"
  sed -i '' 's|{ Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/button"|{ Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"|g' "$file"
  sed -i '' 's|{ Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/button"|{ Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"|g' "$file"
  sed -i '' 's|{ DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/button"|{ DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"|g' "$file"
  sed -i '' 's|{ Popover, PopoverContent, PopoverTrigger } from "@/components/ui/button"|{ Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"|g' "$file"
  sed -i '' 's|{ Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/button"|{ Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"|g' "$file"
  sed -i '' 's|{ Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/button"|{ Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"|g' "$file"
  sed -i '' 's|{ ToggleGroup, ToggleGroupItem } from "@/components/ui/button"|{ ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"|g' "$file"
  sed -i '' 's|{ Breadcrumb, BreadcrumbEllipsis, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/button"|{ Breadcrumb, BreadcrumbEllipsis, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"|g' "$file"
  sed -i '' 's|{ Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/button"|{ Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"|g' "$file"
  sed -i '' 's|{ Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut } from "@/components/ui/button"|{ Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut } from "@/components/ui/command"|g' "$file"
  sed -i '' 's|{ Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/button"|{ Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer"|g' "$file"
  sed -i '' 's|{ Sheet, SheetClose, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/button"|{ Sheet, SheetClose, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"|g' "$file"
  sed -i '' 's|{ Toaster } from "@/components/ui/button"|{ Toaster } from "@/components/ui/sonner"|g' "$file"
  
done

echo "Import replacement completed!"
