# Generate Embeddings Feature

## Overview

The Generate Embeddings feature allows users to create vector embeddings for documents, making them searchable and usable by AI assistants. This feature integrates with an external AI API to process document content and generate vector representations.

## Architecture

### Components

1. **Document Details Panel** (`document-details-panel.tsx`)

   - Provides the UI button to trigger embeddings generation
   - Shows loading state and progress feedback
   - Displays success/error messages via toast notifications

2. **Documents Provider** (`documents-provider.tsx`)

   - Exposes the `generateEmbeddings` function to child components
   - Manages the context for document operations

3. **Generate Embeddings Action** (`generate-embeddings.ts`)

   - Server action that handles document content extraction
   - Prepares the request payload for the external AI API
   - Handles error cases and response processing

4. **Embeddings API Route** (`/api/ai/embeddings/route.ts`)
   - Internal API endpoint that forwards requests to the external AI service
   - Handles authentication and request formatting
   - Provides proper error handling and logging

### Data Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as Document Details Panel
    participant Provider as Documents Provider
    participant Action as Generate Embeddings Action
    participant API as Internal API Route
    participant External as External AI API

    User->>UI: Click "Generate Embeddings"
    UI->>Provider: Call generateEmbeddings(fileId)
    Provider->>Action: Call generateEmbeddingsAction()
    Action->>Action: Extract document content
    Action->>API: POST /api/ai/embeddings
    API->>External: POST /v1/external/embeddings/submit
    External-->>API: Response
    API-->>Action: Response
    Action-->>Provider: Success/Error
    Provider-->>UI: Result
    UI->>User: Show toast notification
```

## Implementation Details

### Document Content Extraction

The system attempts to extract text content from documents using a priority-based approach:

1. **Priority 1**: Direct `content` field (for system-created documents)

   - Strips HTML tags and converts to plain text
   - Handles common HTML entities

2. **Priority 2**: `bufferFile` field (base64 encoded binary)

   - Decodes base64 content
   - Attempts UTF-8 text extraction
   - Validates for readable text content

3. **Priority 3**: Download endpoint fallback
   - Uses the document download action
   - Extracts text from the downloaded buffer

### Request Format

The external AI API expects the following request format:

```json
{
  "documents": [
    {
      "id": "document-id",
      "name": "Document Name",
      "content": "Document text content..."
    }
  ],
  "options": {
    "maxTokens": 2000,
    "overlapTokens": 200,
    "clearExisting": true
  }
}
```

### Configuration

The feature requires the following environment variables:

- `ANTER_API_URL`: Base URL for the external AI API
- `INTERNAL_SECRET`: Authentication secret for API calls
- `ANTER_API_TIMEOUT`: Request timeout (default: 60000ms for embeddings)

## User Experience

### UI Elements

- **Generate Embeddings Button**: Located in the document details panel under the "Actions" section
- **Loading State**: Shows spinner and "Generating..." text during processing
- **Progress Message**: Displays "Processing document content and generating vector embeddings..."
- **Success Notification**: Toast message confirming successful embeddings generation
- **Error Handling**: Clear error messages for various failure scenarios

### Error Scenarios

1. **Document Not Found**: Clear message when document doesn't exist
2. **No Extractable Content**: Warning when document has no readable text
3. **Network Errors**: Timeout and connectivity error messages
4. **API Errors**: External service error messages with context

## Testing

### Manual Testing

1. Navigate to a document in the documents module
2. Open the document details panel
3. Click "Generate Embeddings" button
4. Verify loading state appears
5. Confirm success/error toast notification
6. Check browser console for detailed logs

### Test Cases

- Text documents with direct content
- Binary documents with extractable text
- Documents with no readable content
- Network timeout scenarios
- API error responses

## Future Enhancements

1. **Batch Processing**: Support for generating embeddings for multiple documents
2. **Progress Tracking**: Real-time progress updates for large documents
3. **Content Preview**: Show extracted content before processing
4. **Retry Mechanism**: Automatic retry for transient failures
5. **Embeddings Status**: Display whether a document already has embeddings

## Security Considerations

- All API calls use internal authentication secrets
- Document content is processed server-side only
- No sensitive data is logged in client-side code
- Proper error handling prevents information leakage
