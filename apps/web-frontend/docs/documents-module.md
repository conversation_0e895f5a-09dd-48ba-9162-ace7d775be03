### Documents Module - Architecture, Flows, and Maintenance Guide

#### Overview

The documents module provides creation, upload, listing, filtering, inline editing, and download of organization-scoped documents. It follows API-only data access from the web-frontend and a RLS-aware repository on the backend.

- Frontend (Next.js App Router): `apps/web-frontend`
  - Server actions in `documents/_actions/*` call the backend API
  - UI is composed of a data table, action bar, sheet-based editor, and a provider for coordination
- Backend (NestJS): `apps/api`
  - Controller: `organizations/:organizationId/files` under v1
  - Service delegates to Drizzle repository layer
- Repository (Drizzle): `packages/database-drizzle`
  - `FileRepository` enforces RLS via `SessionContext`
- Shared DTOs & Schemas: `@askinfosec/types` (Zod)

#### Frontend Architecture

- Provider orchestrates UI signals (refresh/open/add), CRUD methods, and upload selection state.
- DataTable fetches via server action with server-side filters, caches per-filter-set, debounces updates, and opens editor sheet per-row.
- Editor sheet updates document content and select fields. Name is editable via inline rename.
- Upload carousel supports drag-and-drop; files are converted to base64 and posted to `/uploads`.

Key provider responsibilities:

```24:62:apps/web-frontend/src/app/(protected)/[organizationId]/(modules)/documents/_components/documents-provider.tsx
export function DocumentsProvider({ organizationId, children }: DocumentsProviderProps) {
  const [refreshVersion, setRefreshVersion] = useState(0);
  const [lastAdded, setLastAdded] = useState<ContractsV1Files.FileDTO | undefined>(undefined);
  const [openDocumentId, setOpenDocumentId] = useState<string | undefined>(undefined);
  // ...
  const requestRefresh = useCallback(() => {
    setRefreshVersion((v) => v + 1);
  }, []);

  const create = useCallback(async (params) => {
    const result = await createDocumentAction(params);
    if (result.success && result.document) {
      addDocumentLocal(result.document);
      openDocumentLocal(result.document.id);
      requestRefresh();
    }
    return result;
  }, [addDocumentLocal, openDocumentLocal, requestRefresh]);
}
```

Listing, cache, and editor open/close flow:

```590:607:apps/web-frontend/src/app/(protected)/[organizationId]/(modules)/documents/_components/documents-data-table.tsx
{openDocId && (
  <DocumentEditorContainer
    document={data.find((d) => d.id === openDocId)!}
    organizationId={organizationId}
    onSave={(updated) => handleUpdateDocument(openDocId, updated)}
    onLocalUpdate={handleLocalUpdate}
    open={true}
    onOpenChange={(isOpen) => {
      if (!isOpen) {
        // Defer list refresh until editor is closed to avoid flicker
        requestRefresh();
      }
      setOpenDocId(isOpen ? openDocId : null);
    }}
  />
)}
```

Inline rename and panel field updates bubble to table without refetch:

```124:134:apps/web-frontend/src/app/(protected)/[organizationId]/(modules)/documents/_components/document-editor/document-editor-container.tsx
<DocumentRename
  documentName={localDoc.name || "Untitled Document"}
  onRename={(newName) => {
    setLocalDoc((prev) => ({ ...prev, name: newName }));
    onLocalUpdate?.({ id: document.id, name: newName });
  }}
/>
```

Download uses a streamed binary endpoint and constructs a Blob client-side:

```444:488:apps/web-frontend/src/app/(protected)/[organizationId]/(modules)/documents/_components/documents-data-table.tsx
const result = await providerDownload(fileId);
// base64 -> Blob -> Object URL -> anchor click
toast.success("Document downloaded successfully");
```

Upload converts to base64 and posts with checksum for integrity:

```63:83:apps/web-frontend/src/app/(protected)/[organizationId]/(modules)/documents/_components/create-document/upload-file-carousel-item.tsx
const rawBuffer = await file.arrayBuffer();
const checksum = await sha256HexFromArrayBuffer(rawBuffer);
const bufferBase64 = await toBase64(file);
const res = await uploadBase64({ organizationId, name: file.name, bufferBase64, checksum });
addDocumentLocal(res.file);
requestRefresh();
```

#### Backend Contract (selected endpoints)

Controller surface and versioning, tenant scoping, and guards:

```28:36:apps/api/src/modules/files/controllers/files.controller.ts
@ApiTags('Files')
@ApiBearerAuth()
@ApiSurface('internal')
@Version('1')
@Controller('organizations/:organizationId/files')
@UseGuards(CustomAuthGuard, RlsContextGuard)
export class FilesController {
  constructor(private readonly filesService: FilesService) {}
```

Create, update, and list use `ExtendedRequest` to pass RLS context:

```281:313:apps/api/src/modules/files/controllers/files.controller.ts
async updateFile(
  @Param('organizationId') organizationId: string,
  @Param('id') id: string,
  @Body() data: ContractsV1Files.FileUpdateRequest,
  @Req() request: ExtendedRequest,
): Promise<ContractsV1Files.FileUpdateResponse> {
  const context = { organizationId, userId: request.userId, bypassRls: request.bypassRls || false };
  return this.filesService.updateFile(id, data, context);
}
```

#### Repository Layer (Drizzle)

`FileRepository` validates `SessionContext` and enforces org scoping. Bulk delete is org-scoped:

```374:379:packages/database-drizzle/src/repositories/file.repository.ts
async deleteFiles(fileIds: string[], context: SessionContext): Promise<boolean> {
  this.validateContext(context, ['organizationId', 'userId']);
  return this.executeQueryWithSession(async (tx) => {
    const result = await tx.delete(files)
      .where(and(inArray(files.id, fileIds), eq(files.organizationId, context.organizationId as string)))
      .returning();
    return result.length > 0;
  }, context, 'deleteFile');
}
```

#### Shared Types

Transport schemas and DTOs are defined with Zod and re-exported as `ContractsV1Files`:

```22:41:packages/types/src/contracts/v1/files.ts
export const FileSchema = z.object({
  id: z.string(),
  name: z.string(),
  // ...
  accessLevel: FileAccessLevelSchema.optional(),
  content: z.string().optional(),
});
```

#### Data Flows

- Create (blank): Carousel -> server action `createDocument` -> POST `/files` -> Provider adds to local list and opens editor -> Table refresh on editor close
- Upload (file): Dropzone -> compute checksum + base64 -> POST `/files/uploads` -> Provider local add -> Table refresh
- List: DataTable -> server action `getAllDocuments` with debounced server-side filters -> cache by normalized filter key
- Update: Editor save or inline field save -> Provider `update` -> PUT `/files/:id` -> Table refresh on next refresh signal
- Delete: Confirm dialog -> Provider `remove` -> DELETE `/files` with `{ ids }` -> success toast -> Table refresh
- Download: Row action -> Provider download -> `GET /files/:id/download` -> Blob -> save

#### Validation, Security, and RLS

- All backend endpoints require `CustomAuthGuard` and `RlsContextGuard`.
- Controller extracts `organizationId` from the URL path; repository methods require `SessionContext`.
- Frontend never accesses DB directly; only API calls.

#### Error Handling Patterns

- Frontend uses `ApiError` to surface request failures; granular toasts for create/update/delete/download.
- Download path avoids JSON parsing and treats response as binary stream.

#### Performance & Caching

- Debounced filter application (300ms) reduces server requests.
- Cache map keyed by normalized filters avoids redundant refetches.
- Editor is hoisted to avoid unmounting on list refresh; list refresh is deferred until editor closes to avoid flicker.

#### Potential Issues (prior to refactor)

- Non-null assertion when opening editor: `data.find(... )!` may crash if rows refresh and the document is missing.
- Editor local state may drift if upstream `document` prop changes while the editor is open and not dirty.
- Debug console logs in `documents-table-action-bar` are noisy for production builds.

#### Recommendations

- Guard editor rendering when the target document cannot be found.
- Sync editor local shadow state from props on document id change or when not dirty.
- Remove transient debug logs from the action bar.

#### Applied Refactors (Done)

- Editor guard added to prevent crashing when the selected document is no longer present.
- Editor local state now syncs from props when not dirty and whenever the document id changes.
- Removed debug logs from the action bar component.

#### Appendix: API Helpers

All API calls go through typed helpers; JSON responses throw `ApiError` on invalid or empty body conditions.

```112:150:apps/web-frontend/src/lib/backend-api.ts
export async function apiRequestJson<TResponse = unknown>(endpoint: string, init: RequestInit = {}): Promise<TResponse> {
  const res = await apiRequest(endpoint, init);
  if (!res.ok) { /* throws ApiError with message/body */ }
  // handles 204 and empty bodies safely
  return (await res.json()) as TResponse;
}
```
