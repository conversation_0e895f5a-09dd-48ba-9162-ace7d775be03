#!/bin/bash

# Better script to fix imports by replacing specific patterns

echo "Fixing imports..."

# Fix Card imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/button"|{ Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"|g'
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Card, CardContent, CardHeader } from "@/components/ui/button"|{ Card, CardContent, CardHeader } from "@/components/ui/card"|g'
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Card, CardContent } from "@/components/ui/button"|{ Card, CardContent } from "@/components/ui/card"|g'
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Card } from "@/components/ui/button"|{ Card } from "@/components/ui/card"|g'

# Fix Dialog imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from "@/components/ui/button"|{ Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from "@/components/ui/dialog"|g'

# Fix Badge imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Badge } from "@/components/ui/button"|{ Badge } from "@/components/ui/badge"|g'

# Fix Input imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Input } from "@/components/ui/button"|{ Input } from "@/components/ui/input"|g'

# Fix Label imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Label } from "@/components/ui/button"|{ Label } from "@/components/ui/label"|g'

# Fix Separator imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Separator } from "@/components/ui/button"|{ Separator } from "@/components/ui/separator"|g'

# Fix Table imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/button"|{ Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"|g'

# Fix Select imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/button"|{ Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"|g'

# Fix Form imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/button"|{ Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"|g'

# Fix DropdownMenu imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/button"|{ DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"|g'

# Fix Popover imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Popover, PopoverContent, PopoverTrigger } from "@/components/ui/button"|{ Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"|g'

# Fix Accordion imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/button"|{ Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"|g'

# Fix Tabs imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/button"|{ Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"|g'

# Fix ToggleGroup imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ ToggleGroup, ToggleGroupItem } from "@/components/ui/button"|{ ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"|g'

# Fix Breadcrumb imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Breadcrumb, BreadcrumbEllipsis, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/button"|{ Breadcrumb, BreadcrumbEllipsis, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"|g'

# Fix Carousel imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/button"|{ Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"|g'

# Fix Command imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut } from "@/components/ui/button"|{ Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut } from "@/components/ui/command"|g'

# Fix Drawer imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/button"|{ Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer"|g'

# Fix Sheet imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Sheet, SheetClose, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/button"|{ Sheet, SheetClose, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"|g'

# Fix Sonner imports
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|{ Toaster } from "@/components/ui/button"|{ Toaster } from "@/components/ui/sonner"|g'

echo "Import fixing completed!"
